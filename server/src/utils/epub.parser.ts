import * as epubParser from "epub-parser";
import * as cheerio from 'cheerio';
import { basename, join } from 'path';
import { writeFileSync } from 'fs';

export class EpubParser {
  private path: string = null
  private imageDir: string = './upload/epub/images'

  constructor(path: string) {
    this.path = path;
  }

  parse() {
    return new Promise((resolve, reject) => {
      epubParser.open(this.path, (err, data) => {
        if(err) return reject(err);

        const chapters = this.getChapters(data)
        resolve(chapters);
      })
    })
  }

  getChapters(data) {
    return data.raw.json.ncx.navMap[0].navPoint.map((e: any) => {
      const content = this.getContent(e.content[0].$.src.split('#')[0]);
      const $ = cheerio.load(content);
      const imageDir = this.imageDir;
      const baseUrl = process.env.BUILD_ENV === 'local' ? 'http://localhost:9015' : 'https://dev.advayta.org'

      $('img').each(function() {
        const src = $(this).attr('src');
        const binary = epubParser.extractBinary(src)

        const fileName = basename(src);
        const imagePath = join(imageDir, fileName);

        writeFileSync(imagePath, binary, 'binary');

        $(this).attr('src', `${baseUrl}/${imagePath}`);
      })

      return {
        title: e.navLabel[0].text[0],
        content: $.html()
      }
    })
  }

  getContent(href: string) {
    return epubParser.extractText(href)
  }
}