import { Injectable } from '@nestjs/common';
import {Library} from "@/entity/Library";
import {LibraryTranslation} from "@/entity/LibraryTranslation";
import {User} from "@/entity/User";
import {LibraryQuote} from "@/entity/LibraryQuote";
import {HttpService} from "@nestjs/axios";
import {ContentQuote} from "@/entity/ContentQuote";
import {Brackets, In} from "typeorm";
import {AudioTag} from "@/entity/AudioTag";
import {Content} from "@/entity/Content";
import {Like} from "@/entity/Like";
import {AudioAuthor} from "@/entity/AudioAuthor";
import {Audio} from "@/entity/Audio";
import {LibraryCategory} from "@/entity/LibraryCategory";
import { LibraryAuthor } from '@/entity/LibraryAuthor';

const SIMILAR_LIBRARY_LIMIT = 3;

@Injectable()
export class LibraryService {
    constructor(private readonly httpService: HttpService) {}

    async getAll(filters: any, user: any = null) {
        const lang = filters.lang;
        const page = parseInt(filters.page) || 1;
        const itemsPerPage = 10;
        let tags = filters.tags;
        let libraryFavourites: any = []

        if(user) {
            libraryFavourites = (await User.findOne({
                where: {
                    id: user.id
                },
                relations: {
                    libraryFavourites: true
                }
            })).libraryFavourites;
            libraryFavourites = libraryFavourites.map((e: any) => e.id)
        }


        const queryBuilder = Library.createQueryBuilder('library')
            .leftJoinAndSelect('library.translations', 'translations')
            .leftJoinAndSelect('translations.likes', 'likes')
            .leftJoinAndSelect('translations.tags', 'tags')
            .select([
                'library',
                'translations.id',
                'translations.created_at',
                'translations.external_id',
                'translations.lang',
                'translations.code',
                'translations.title',
                'translations.author',
                'translations.reader',
                'translations.seo_title',
                'translations.seo_description',
                'translations.pages',
                'translations.category',
                'translations.recomendation',
                'translations.image',
                'translations.access',
                'translations.annotation',
                'translations.content',
                'translations.format',
                'translations.link',
                'translations.summary',
                'translations.audio',
                'translations.views',
                'translations.tagsString',
                'translations.linkShop',
                'translations.linkShopOnline',
                'translations.duration',
                'likes',
                'tags'
            ])
            .take(itemsPerPage)
            .skip((page - 1) * itemsPerPage);

        if (lang) {
            queryBuilder.andWhere('translations.lang = :lang', { lang });
        }

        if (filters.description) {
            queryBuilder.andWhere(
                new Brackets((qb) => {
                    qb.orWhere('translations.title ILIKE :searchTerm', { searchTerm: `%${filters.description}%` });
                    qb.orWhere('translations.author ILIKE :searchTerm', { searchTerm: `%${filters.description}%` });
                    qb.orWhere('translations.reader ILIKE :searchTerm', { searchTerm: `%${filters.description}%` });
                    qb.orWhere('translations.category ILIKE :searchTerm', { searchTerm: `%${filters.description}%` });
                    qb.orWhere('tags.name ILIKE :searchTerm', { searchTerm: `%${filters.description}%` });
                }),
            );
        }

        if(filters.author) {
            if(!Array.isArray(filters.author)) {
                filters.author = [filters.author];
            }
            queryBuilder.andWhere('translations.author IN (:...author)', { author: filters.author });
        }

        if(filters.audio == 'true') {
            queryBuilder.andWhere('translations.audio::jsonb != \'[]\'::jsonb');
        }

        if(filters.paid == 'true') {
          queryBuilder.andWhere('translations.paid = :paid', { paid: true });
        }

        const sortOrderMap = {
            viewsDesc: { field: 'views', direction: 'DESC' },
            viewsAsc: { field: 'views', direction: 'ASC' },
            titleAsc: { field: 'title', direction: 'ASC' },
            titleDesc: { field: 'title', direction: 'DESC' },
            dateDesc: { field: 'created_at', direction: 'DESC' },
            dateAsc: { field: 'created_at', direction: 'ASC' },
        };

        if (filters.sortOrder && sortOrderMap[filters.sortOrder]) {
            const { field, direction } = sortOrderMap[filters.sortOrder];
            queryBuilder.orderBy(`translations.${field}`, direction);
        } else {
            queryBuilder.orderBy('translations.views', 'DESC');
        }

        if (tags) {
            if (!Array.isArray(tags)) {
                tags = [tags];
            }
            queryBuilder.andWhere('tags.id IN (:...tags)', { tags });
        }

        if(filters.category) {
            if (!Array.isArray(filters.category)) {
                filters.category = [filters.category];
            }
            queryBuilder.andWhere('category IN (:...tags)', { tags: filters.category });
        }

        let [items, total, audioTags] = await Promise.all([
            queryBuilder.getMany(),
            queryBuilder.getCount(),
            AudioTag.find()
        ]);

        for(let i in items) {
            for(let t in items[i].translations) {
                items[i].translations[t] = {
                    ...items[i].translations[t],
                    likes: items[i].translations[t].likes ? items[i].translations[t].likes.length : 0,
                    liked: user && items[i].translations[t].likes && items[i].translations[t].likes.some((k: any) => k.id == user.id),
                    inFavourites: libraryFavourites.includes(items[i].translations[t].id)
                } as any
            }
        }


        return {
            items,
            audioTags,
            authors: await LibraryAuthor.find(),
            categories: await LibraryCategory.find(),
            pagination: {
                total, page, itemsPerPage,
                totalPages: Math.ceil(total / itemsPerPage)
            }
        };
    }

    async getByCode(code: string, lang: string, views: boolean) {
        let library = await this.getLibrary(code, lang)
        if(!library) library = await this.getLibrary(code, 'ru');
        if(views) {
            library.translations[0].views++;
            library.translations[0].save()
        }
        return library.translations[0]
    }

    async getLibrary(code: string, lang: string) {
        return await Library.findOne({
            where: {
                code,
                translations: {
                    lang
                }
            },
            select: {
                translations: {
                    id: true,
                    external_id: true,
                    lang: true,
                    code: true,
                    title: true,
                    author: true,
                    reader: true,
                    seo_title: true,
                    seo_description: true,
                    pages: true,
                    category: true,
                    recomendation: true,
                    image: true,
                    access: true,
                    annotation: true,
                    content: true,
                    format: true,
                    link: true,
                    summary: true,
                    audio: true,
                    views: true,
                    tagsString: true,
                    linkShop: true,
                    linkShopOnline: true,
                    duration: true,
                    paid: true
                }
            },
            relations: ['translations.tags', 'translations.likes']
        })
    }

    async addToFavourites(user: any, id: number) {
        const libraryTranslation = await LibraryTranslation.findOneBy({id})
        const userEntity = await User.findOne({
            where: {
                id: user.id
            },
            relations: ['libraryFavourites']
        })
        userEntity.libraryFavourites = userEntity.libraryFavourites.find(e => e.id == id) ? userEntity.libraryFavourites.filter(e => e.id != id) : [...userEntity.libraryFavourites, libraryTranslation]
        await userEntity.save()
    }

    async like(user: any, id: number) {
        const libraryTranslation = await LibraryTranslation.findOneBy({id})
        const userEntity = await User.findOne({
            where: {
                id: user.id
            },
            relations: ['libraryLikes']
        })
        userEntity.libraryLikes = userEntity.libraryLikes.find(e => e.id == id) ? userEntity.libraryLikes.filter(e => e.id != id) : [...userEntity.libraryLikes, libraryTranslation]
        await userEntity.save()
    }

    async addQuoteToFavourites(user: any, body: any) {
        const userEntity = await User.findOne({where: {id: user.id}, relations: ['quoteFavourites', 'subscriptions']});
        const library = await LibraryTranslation.findOneBy({id: body.id})

        if(!userEntity.subscriptions.some(e => ['LIBRARY', 'AUDIO_AND_LIBRARY', 'FULL_ACCESS', 'LIBRARY_AND_COURSES'].includes(e.type))) {
          const countQuotes = await LibraryQuote.countBy({ library: { id: library.id } })
          if(countQuotes >= 50) return { error: 'Превышен лимит цитат. Бесплатная подписка позволяет добавить не более 50 цитат'};
        }

        const quote = await LibraryQuote.save({
            page: body.page,
            quote: body.quote,
            share: body.share,
            library
        })
        userEntity.quoteFavourites = [...userEntity.quoteFavourites, quote]
        await userEntity.save()
        return quote.id
    }

    async deleteQuote(user: any, quote: string) {
        const [content, library] = await Promise.all([
            ContentQuote.findOneBy({quote}),
            LibraryQuote.findOneBy({quote})
        ])
        return content ? await ContentQuote.delete(content.id) : await LibraryQuote.delete(library.id)
    }

    async getQuote(id: number) {
        return await LibraryQuote.findOneBy({id})
    }

    async getLikes(userId: number, slug: string) {
        const userEntity = await User.findOne({where: {id: userId}, relations: ['libraryLikes']});
        const library = await LibraryTranslation.findOne({where: {code: slug}, relations: ['likes']})

        return [
            userEntity.libraryLikes.map(e => e.id),
            library?.likes ? library.likes.length : 0
        ]
    }

    async getFavourites(userId: number, all = false, page = 1) {
        const itemsPerPage = 5;
        const userEntity = await User.findOne({
            where: {
                id: userId
            },
            relations: ['libraryFavourites.likes'],
        });

        if (!userEntity) {
            throw new Error('User not found');
        }

        if (!all) return userEntity.libraryFavourites.map(e => e.id);

        const startIndex = (page - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        let paginatedFavourites = userEntity.libraryFavourites.slice(startIndex, endIndex);

        paginatedFavourites = paginatedFavourites.map((e: any) => {
            return {
                ...e,
                likes: e.likes.length,
                liked: e.likes.some((e: any) => e.id == userId)
            }
        })

        return {
            items: paginatedFavourites,
            pagination: {
                total: userEntity.libraryFavourites.length,
                page,
                itemsPerPage,
                totalPages: Math.ceil(userEntity.libraryFavourites.length / itemsPerPage)
            }
        };
    }

    async getQuoteFavourites(userId: number) {
        const userEntity = await User.findOne({where: {id: userId}, relations: ['quoteFavourites', 'quoteFavourites.library']});
        const favourites = userEntity.quoteFavourites ? userEntity.quoteFavourites.filter((e: any) => e.share == false) : [];
        return favourites;
    }

    async getQuoteFavouritesContent(userId: number) {
        const userEntity = await User.findOne({where: {id: userId}, relations: ['quoteFavouritesContent', 'quoteFavouritesContent.content']});
        const favourites = userEntity.quoteFavouritesContent ? userEntity.quoteFavouritesContent.filter((e: any) => e.share == false) : [];
        return favourites;
    }


    async getChapter(translationId: number, index: number) {
        const library = await LibraryTranslation.findOne({
            where: {id: translationId},
            select: {
                chapters: true
            }
        });
        return {
            content: library.chapters[index],
            count: library.chapters.length,
            titles: library.chapters.map((e: any) => e.title)
        }
    }
    async getSimilar(code: string) {
        const current = await LibraryTranslation.findOne({
            where: { code },
            relations: ['tags'],
        });

        if (!current || current.tags.length === 0) {
            return await this.getRandomLibrary(SIMILAR_LIBRARY_LIMIT, code);
        }

        const tagIds = current.tags.map(tag => tag.id);

        const rawResults = await LibraryTranslation
            .createQueryBuilder('lt')
            .leftJoin('lt.tags', 'tag')
            .where('lt.code != :code', { code })
            .andWhere('tag.id IN (:...tagIds)', { tagIds })
            .select('lt.id', 'id')
            .addSelect('lt.code', 'code')
            .addSelect('COUNT(tag.id)', 'match_count')
            .groupBy('lt.id, lt.code')
            .orderBy('COUNT(tag.id)', 'DESC')
            .limit(SIMILAR_LIBRARY_LIMIT * 3)
            .getRawMany();

        let similarLibraries: LibraryTranslation[] = [];
        const similarItems = rawResults.map(row => ({ id: row.id, code: row.code }));

        if (similarItems.length > 0) {
            const uniqueItems = similarItems.filter((item, index, self) =>
                index === self.findIndex(t => t.code === item.code)
            );

            const shuffledItems = uniqueItems
                .sort(() => 0.5 - Math.random())
                .slice(0, SIMILAR_LIBRARY_LIMIT);

            similarLibraries = await LibraryTranslation.find({
                where: { id: In(shuffledItems.map(item => item.id)) },
                relations: ['tags'],
            });
        }

        if (similarLibraries.length < SIMILAR_LIBRARY_LIMIT) {
            const excludeCodes = [
                code,
                ...similarLibraries.map(lib => lib.code)
            ].filter((v, i, a) => a.indexOf(v) === i);

            const neededCount = SIMILAR_LIBRARY_LIMIT - similarLibraries.length;
            const randomLibraries = await this.getRandomLibrary(neededCount, excludeCodes);
            similarLibraries = [...similarLibraries, ...randomLibraries];
        }

        const uniqueLibraries = similarLibraries.filter((lib, index, self) =>
            index === self.findIndex(t => t.code === lib.code)
        );

        return uniqueLibraries.slice(0, SIMILAR_LIBRARY_LIMIT);
    }

    async getRandomLibrary(limit: number, excludeCode?: string | string[]) {
        const query = LibraryTranslation.createQueryBuilder('lt')
            .where('lt.lang = :lang', { lang: 'ru' })
            .leftJoinAndSelect('lt.tags', 'tags');

        if (excludeCode) {
            if (Array.isArray(excludeCode)) {
                if (excludeCode.length > 0) {
                    query.andWhere('lt.code NOT IN (:...excludeCodes)', { excludeCodes: excludeCode });
                }
            } else {
                query.andWhere('lt.code != :excludeCode', { excludeCode });
            }
        }

        const totalCount = await query.getCount();

        if (totalCount === 0) {
            return [];
        }

        if (totalCount <= limit) {
            return query.getMany();
        }

        const skip = Math.floor(Math.random() * (totalCount - limit));
        return query
            .skip(skip)
            .take(limit)
            .getMany();
    }
}
