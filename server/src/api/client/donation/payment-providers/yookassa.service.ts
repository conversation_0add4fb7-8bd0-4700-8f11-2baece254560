import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class YookassaService {
  private readonly apiUrl = 'https://api.yookassa.ru/v3/payments';
  private readonly shopId: string;
  private readonly secretKey: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {
    this.shopId = this.configService.get<string>('YOOKASSA_SHOP_ID');
    this.secretKey = this.configService.get<string>('YOOKASSA_SECRET_KEY');
  }

  async createPayment(amount: number, currency: string, description: string, metadata: any = {}) {
    const idempotenceKey = uuidv4();

    const autoRenew = metadata?.autoRenew;

    const payload: any = {
      amount: { value: amount.toFixed(2), currency },
      confirmation: {
        type: 'redirect',
        return_url: this.configService.get<string>('DONATION_SUCCESS_URL'),
      },
      capture: true,
      description,
      metadata,
    };

    if (autoRenew) {
      payload.save_payment_method = true;
      payload.payment_method_data = { type: 'bank_card' };
    }

    const headers = {
      'Content-Type': 'application/json',
      'Idempotence-Key': idempotenceKey,
      'Authorization': 'Basic ' + Buffer.from(`${this.shopId}:${this.secretKey}`).toString('base64'),
    };

    try {
      const response = await firstValueFrom(this.httpService.post(this.apiUrl, payload, { headers }));
      const paymentUrl = response.data.confirmation.confirmation_url;
      return { paymentUrl };
    } catch {
      throw new InternalServerErrorException('Ошибка при создании платежа через ЮKassa');
    }
  }

  cancelAutoRenew(subscriptionId: string) {}
}