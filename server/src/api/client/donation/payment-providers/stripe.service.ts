import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Stripe from 'stripe';

@Injectable()
export class StripeService {
  private stripe: Stripe;

  constructor(private readonly configService: ConfigService) {
    this.stripe = new Stripe(this.configService.get<string>('STRIPE_SECRET_KEY'));
  }

  async createSubscriptionCheckout(priceId: string, customerEmail: string | null, description: string, metadata = {}) {
    try {
      const session = await this.stripe.checkout.sessions.create({
        mode: 'subscription',
        payment_method_types: ['card'],
        line_items: [{ price: priceId, quantity: 1 }],
        success_url: this.configService.get<string>('DONATION_SUCCESS_URL'),
        customer_email: customerEmail || undefined,
        metadata,
        subscription_data: {
          metadata,
        },
      });

      return { paymentUrl: session.url };
    } catch (error) {
      throw new InternalServerErrorException('Ошибка при создании подписки через Stripe');
    }
  }

  async createPayment(amount: number, currency: string, description: string, metadata: any = {}) {
    try {
      if(metadata?.autoRenew) {
        const session = await this.stripe.checkout.sessions.create({
          mode: 'subscription',
          line_items: [
            {
              quantity: 1,
              price_data: {
                currency,
                unit_amount: amount,
                product_data: {
                  name: description,
                },
                recurring: {
                  interval: 'month',
                  interval_count: 1,
                },
              },
            },
          ],
          success_url: this.configService.get<string>('DONATION_SUCCESS_URL'),
          subscription_data: { description, metadata }
        });

        return { paymentUrl: session.url };
      } else {
        const session = await this.stripe.checkout.sessions.create({
          payment_method_types: ['card'],
          line_items: [
            {
              price_data: {
                currency: currency.toLowerCase(),
                product_data: {
                  name: description,
                },
                unit_amount: Math.round(amount * 100),
              },
              quantity: 1,
            },
          ],
          mode: 'payment',
          success_url: this.configService.get<string>('DONATION_SUCCESS_URL'),
          metadata,
          payment_intent_data: {
            metadata
          }
        });

        return { paymentUrl: session.url };
      }
    } catch (error) {
      throw new InternalServerErrorException('Ошибка при создании платежа через Stripe');
    }
  }

  async cancelAutoRenew(subscriptionId: string) {
    return await this.stripe.subscriptions.update(subscriptionId, { cancel_at_period_end: true });
  }
}