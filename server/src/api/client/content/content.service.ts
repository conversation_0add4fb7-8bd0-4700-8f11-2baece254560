import { Injectable } from '@nestjs/common';
import { Content } from "@/entity/Content";
import {Favourite} from "@/entity/Favourite";
import {User} from "@/entity/User";
import {Like} from "@/entity/Like";
import {ContentCategory} from "@/entity/ContentCategory";
import {ILike, In} from "typeorm";
import {ContentQuote} from "@/entity/ContentQuote";

const SIMILAR_CONTENT_LIMIT = 3;

@Injectable()
export class ContentService {
  async getAll(filter: any, user: any = null) {
    let where: any = {lang: filter.lang, active: true}
    let order: any = {}

    if(typeof filter.tag === 'string') {
      filter.tag = filter.tag.split(',')
    }
  
    if(filter && filter.category) {
      where['category'] = {id: filter.category};
    }
  
    if(filter && filter.search) {
      where['title'] = ILike(`%${filter.search}%`);
    }
  
    if(filter && filter.sortOrder) {
      if(filter.sortOrder === 'dateAsc' || filter.sortOrder === 'dateDesc') {
        order['created_at'] = filter.sortOrder === 'dateAsc' ? 'ASC' : 'DESC'
      }

      if(filter.sortOrder === 'viewsAsc' || filter.sortOrder === 'viewsDesc') {
        order['views'] = filter.sortOrder === 'viewsAsc' ? 'ASC' : 'DESC'
      }

      if(filter.sortOrder === 'titleAsc' || filter.sortOrder === 'titleDesc') {
        order['title'] = filter.sortOrder === 'titleAsc' ? 'ASC' : 'DESC'
      }
    }
  
    if(filter && filter.tag && filter.tag.length) {
      where['tags'] = {
          id: In(filter.tag)
      };
    }
  
    // Pagination parameters
    const page = filter && filter.page ? parseInt(filter.page) : 1;
    const itemsPerPage = filter && filter.itemsPerPage ? parseInt(filter.itemsPerPage) : 10;
    const skip = (page - 1) * itemsPerPage;
  
    // Get total count for pagination metadata
    const total = await Content.count({
      where
    });
  
    // Get paginated results
    let items: any = await Content.find({
      where,
      order,
      relations: ['preview', 'category', 'tags', 'likes.user', 'favourites.user'],
      skip: skip,
      take: itemsPerPage
    });

    items = items.map(e => {
      return {
        ...e,
        likes: e.likes.length,
        liked: user && e.likes.some(k => k.user.id == user.id),
        inFavourites: user && e.favourites.some(k => k.user.id == user.id),
      }
    })

    return {
      items,
      pagination: {
        total,
        page,
        itemsPerPage,
        totalPages: Math.ceil(total / itemsPerPage)
      }
    };
  }
  

  async getOne(page: string, lang: string, views: boolean, user: any = null) {
    const content: any = await Content.findOne({
      where: {slug: page, lang},
      relations: ['favourites', 'likes', 'tags', 'category', 'preview', 'likes.user', 'favourites.user', 'audioFiles', 'audio'],
    });
    if(views && content) await Content.update(content.id, {views: ++content.views});

    content.liked = user ? content.likes.some(k => k.user.id == user.id) : false;
    content.inFavourites = user ? content.favourites.some(k => k.user.id == user.id) : false;
    content.likes = content.likes.length;

    return content
  }

  async getLink(id: number) {
    return await Content.findOne({
      where: {id},
      relations: ['category']
    })
  }

  async favourite(id: number, userId: number) {
    const user = await User.findOneBy({id: userId});
    const favouriteItem = await Favourite.findOneBy({parent_id: id, user: {id: userId}})
    if(favouriteItem) {
      return await Favourite.delete(favouriteItem.id)
    }
    const content = await Content.findOne({
      where: {id},
      relations: {
        favourites: true
      }
    })
    const favourite = await Favourite.save({
      parent_id: id,
      user
    })
    content.favourites = [...content.favourites, favourite]
    await content.save()
  }

  async like(id: number, userId: number) {
    const user = await User.findOneBy({id: userId});
    const likeItem = await Like.findOneBy({parent_id: id, user: {id: userId}})
    if(likeItem) {
      return await Like.delete(likeItem.id)
    }
    const content = await Content.findOne({
      where: {id},
      relations: {
        likes: true
      }
    })
    const like = await Like.save({
      parent_id: id,
      user
    })
    content.likes = [...content.likes, like]
    await content.save()
  }

  async getCategories() {
    return await ContentCategory.find({
      where: {
        active: true
      },
      relations: ['preview'],
      order: {
        order: 'ASC'
      }
    })
  }

  async addQuoteToFavourites(user: any, body: any) {
    const userEntity = await User.findOne({where: {id: user.id}, relations: ['quoteFavouritesContent']});
    const content = await Content.findOneBy({id: body.id})
    const quote = await ContentQuote.save({
      quote: body.quote,
      share: body.share,
      content
    })
    userEntity.quoteFavouritesContent = [...userEntity.quoteFavouritesContent, quote]
    await userEntity.save()
    return quote.id
  }

  async getQuote(id: number) {
    return await ContentQuote.findOneBy({id})
  }

  async getLikes(userId: number, slug: string) {
    const userEntity = await User.findOne({where: {id: userId}, relations: ['likesContent']});
    const content = await Content.findOneBy({slug})
    const likesCount = await Like.count({
      where: {
        parent_id: content.id
      }
    })
    return [
        userEntity.likesContent.map(e => e.parent_id),
        likesCount
    ]
  }

  async getFavourites(userId: number) {
    const userEntity = await User.findOne({where: {id: userId}, relations: ['favouriteContent']});
    return userEntity.favouriteContent.map(e => e.parent_id)
  }

  async getFavouritesByIds(ids: number[], page: number = 1, user = null) {
    const itemsPerPage = 5;
    const params = {
      where: {
        id: In(ids)
      },
      relations: ['category', 'likes.user']
    }
    let items: any = await Content.find({
      ...params,
      take: itemsPerPage,
      skip: (page - 1)*itemsPerPage,
    });
    const total = await Content.count(params);

    items = items.map(e => {
      return {
        ...e,
        likes: e.likes.length,
        liked: user ? e.likes.some(k => k.user.id == user.id) : false
      }
    })

    return {
      items,
      pagination: {
          total,
          page,
          itemsPerPage,
          totalPages: Math.ceil(total / itemsPerPage)
      }
    }
  }

  async getSimilar(slug: string): Promise<Content[]> {
    const current = await Content.findOne({
      where: { slug },
      relations: ['tags'],
    });

    if (!current || current.tags.length === 0) {
      return this.getRandomContents(SIMILAR_CONTENT_LIMIT, slug);
    }

    const tagIds = current.tags.map(tag => tag.id);

    const rawResults = await Content
        .createQueryBuilder('content')
        .leftJoin('content.tags', 'tag')
        .where('content.slug != :slug', { slug })
        .andWhere('tag.id IN (:...tagIds)', { tagIds })
        .select('content.id', 'id')
        .addSelect('content.slug', 'slug')
        .addSelect('COUNT(tag.id)', 'match_count')
        .groupBy('content.id, content.slug')
        .orderBy('COUNT(tag.id)', 'DESC')
        .limit(SIMILAR_CONTENT_LIMIT * 3)
        .getRawMany();

    let similarContents: Content[] = [];
    const similarItems = rawResults.map(row => ({ id: row.id, slug: row.slug }));

    if (similarItems.length > 0) {
      const uniqueItems = similarItems.filter((item, index, self) =>
          index === self.findIndex(t => t.slug === item.slug)
      );

      const shuffledItems = uniqueItems
          .sort(() => 0.5 - Math.random())
          .slice(0, SIMILAR_CONTENT_LIMIT);

      similarContents = await Content.find({
        where: { id: In(shuffledItems.map(item => item.id)) },
        relations: ['tags', 'category', 'preview'],
      });
    }

    if (similarContents.length < SIMILAR_CONTENT_LIMIT) {
      const excludeSlugs = [
        slug,
        ...similarContents.map(content => content.slug)
      ].filter((v, i, a) => a.indexOf(v) === i); // Уникальные значения

      const neededCount = SIMILAR_CONTENT_LIMIT - similarContents.length;
      const randomContents = await this.getRandomContents(neededCount, excludeSlugs);
      similarContents = [...similarContents, ...randomContents];
    }

    const uniqueContents = similarContents.filter((content, index, self) =>
        index === self.findIndex(t => t.slug === content.slug)
    );

    return uniqueContents.slice(0, SIMILAR_CONTENT_LIMIT);
  }

  async getRandomContents(limit: number, excludeSlug?: string | string[]) {
    const query = Content.createQueryBuilder('content')
        .leftJoinAndSelect('content.tags', 'tags')
        .leftJoinAndSelect('content.category', 'category')
        .leftJoinAndSelect('content.preview', 'preview');

    if (excludeSlug) {
      if (Array.isArray(excludeSlug)) {
        if (excludeSlug.length > 0) {
          query.andWhere('content.slug NOT IN (:...excludeSlugs)', {
            excludeSlugs: excludeSlug
          });
        }
      } else {
        query.andWhere('content.slug != :excludeSlug', {
          excludeSlug
        });
      }
    }

    const totalCount = await query.getCount();

    if (totalCount === 0) {
      return [];
    }

    if (totalCount <= limit) {
      return query.getMany();
    }

    const skip = Math.floor(Math.random() * (totalCount - limit));
    return query
        .skip(skip)
        .take(limit)
        .getMany();
  }
}
