import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy, VerifyCallback } from 'passport-google-oauth20';
import {UserService} from "@/api/user/user.service";
import {JwtService} from "@nestjs/jwt";
import * as generatePassword from "password-generator";
import * as process from 'node:process';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import {writeFileSync} from 'fs';
import { FileService } from '@/api/file/file.service';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
    constructor(
      private userService: UserService,
      private jwtService: JwtService,
      private httpService: HttpService,
      private fileService: FileService,
    ) {
        super({
            clientID: process.env.GOOGLE_CLIENT_ID,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET,
            callbackURL: `${process.env.BUILD_ENV == 'local' ? 'http://localhost:9015' : 'https://dev.advayta.org'}/api/user/google/callback`,
            scope: ['email', 'profile'],
        });
    }

    async validate(
        accessToken: string,
        refreshToken: string,
        profile: any,
        done: VerifyCallback,
    ): Promise<any> {
        let user = await this.userService.getByEmail(profile.emails[0].value);
            if(!user) {
                const password = generatePassword(8, false);

                const photo = profile.photos[0].value;
                const response = await firstValueFrom(this.httpService.get(photo, {responseType: 'arraybuffer'}));
                const avatarName = `${profile.name?.familyName}.png`;
                writeFileSync(`./upload/tmp/${avatarName}`, response.data);
                const avatar = await this.fileService.save(avatarName, 'avatar', `tmp/${avatarName}`);

                user = await this.userService.signUp({
                    email: profile.emails[0].value,
                    firstName: profile.name?.givenName || null,
                    lastName: profile.name?.familyName || null,
                    middleName: profile.name?.familyName || null,
                    password: password,
                    confirmPassword: password,
                    avatar
                }, false)
            }
        done(null, this.userService.createAccessToken(user));
    }
}