import { Component, effect, EventEmitter, HostListener, inject, Output, PLATFORM_ID, ViewEncapsulation } from '@angular/core';
import { CommonModule, isPlatformBrowser, NgOptimizedImage, Location, NgClass } from '@angular/common';
import { FormsModule } from "@angular/forms";
import { SearchService } from "@/services/search.service";
import { Router, ActivatedRoute, RouterModule, RouterLink } from "@angular/router";
import { TranslocoService } from "@jsverse/transloco";
import { environment } from "@/env/environment";
import { AuthService } from '@/services/auth.service';
import { LibraryService } from '@/services/library.service';
import { ShareDataService } from '@/services/share-data.service';
import { ToasterService } from '@/services/toaster.service';
import { ProfileService } from '@/services/profile.service';
import { ReadingTimePipe } from '@/pipes/reading-time.pipe';
import { ContentService } from '@/services/content.service';
import { IsInPlaylistPipe } from '@/pipes/isInPlaylist.pipe';
import { Track } from '@/interfaces/track';
import { PlaylistDialogComponent } from '@/pages/audio-gallery/playlist-dialog/playlist-dialog.component';
import { AudioService } from '@/services/audio.service';
import {PhotoService} from "@/services/photo.service";
import moment from "moment";
import 'moment/locale/ru';
import {ForumService} from "@/services/forum.service";
moment.locale('ru');

export enum SearchTabTypes {
  ALL = 'Все',
  CONTENT = 'Статьи',
  LIBRARY = 'Книги',
  LECTURES = 'Лекции',
  PHOTO = 'Фото',
  FORUM = 'Форум'
}
@Component({
  selector: 'app-search',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    RouterLink,
    NgOptimizedImage,
    ReadingTimePipe,
    PlaylistDialogComponent,
    IsInPlaylistPipe,
    NgClass
  ],
  templateUrl: './search.component.html',
  styleUrl: './search.component.scss',
  encapsulation: ViewEncapsulation.None
})
export class SearchComponent {
  @Output() searchShowChanged: EventEmitter<boolean> = new EventEmitter();
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const clickedElement = event.target as HTMLElement;
    const hasOpenMenu = Object.values(this.show_menu).some(isOpen => isOpen);
    if (hasOpenMenu) {
      const isMenuToggleClick = clickedElement.closest('.actions_w');
      if (!isMenuToggleClick) {
        this.show_menu = {};
      }
    }
  }
  playlists: any[] = [];
  platformId = inject(PLATFORM_ID);
  contentService = inject(ContentService);
  audioService = inject(AudioService);
  searchService: SearchService = inject(SearchService);
  translocoService = inject(TranslocoService);
  private route = inject(ActivatedRoute);
  shareDataService = inject(ShareDataService)
  profileService = inject(ProfileService);
  toasterService = inject(ToasterService);
  libraryService = inject(LibraryService);
  photoService = inject(PhotoService);
  forumService = inject(ForumService);
  location = inject(Location);
  protected readonly environment = environment;
  protected readonly Math = Math;
  showPlaylist = false;
  selectedTrackId: number = -1;
  router = inject(Router);
  lang = 'ru';
  show_menu: Record<number, boolean> = {};
  tabs: SearchTabTypes[] = Object.values(SearchTabTypes);
  activeTab: SearchTabTypes = this.tabs[0];
  selectedDropdElement: any = null;
  isOpened: Record<number, boolean> = {};
  isOpened_: Record<number, boolean> = {};
  isOpened__: Record<number, boolean> = {};
  authService = inject(AuthService)
  query: string | null = null
  items: any = []
  labels: any = {
    'content': 'Статьи',
    'library': 'Книги',
    'audio': 'Лекции',
    'photo': 'Фото',
    'forum': 'Форум',
  }
  data: any = [];
  selectedTags: any[] = [];
  favouriteContent: any = [];
  quoteActions = [
    'копировать',
    'удалить',
    'поделиться',
    'мне нравится',
  ];
  forumActions = [
    'удалить',
    'поделиться',
  ];
  page: number = this.route.snapshot.queryParams['page'] || 1
  totalItems: number = 0
  totalPages: number = Math.ceil(this.totalItems / 10) + 1

  // Массивы для рандомных сообщений
  emptySearchMessages = [
    'Ничего не найдено, все состоит из пустоты!',
    'То, что не найдено, никогда и не терялось',
    'Истинный искатель находит себя в отсутствии находок',
    'Пустота — это полнота, лишенная содержания'
  ];

  initialMessages = [
    'Сознание исследует свои проявления...',
    'В тишине ума рождается поиск истины',
    'Что ищет тот, кто уже есть всё?',
    'Пространство осознавания готово принять запрос',
    'Волны поиска расходятся по океану знания...',
    'Кто ищет? Что ищется? Где граница между ними?',
    'Ахам брахмасми — даже в пустом поиске'
  ];

  currentRandomMessage = '';
  isInitialState = true;
  isLoading = false;

  constructor() {
    // Устанавливаем начальное рандомное сообщение
    this.setRandomMessage(this.initialMessages);

    effect(() => {
      if (this.contentService.list()?.length > 0) {
        const newItems = this.contentService.list();

        if (!this.data.length) {
          this.data = [...newItems];
        } else {
          const updatedData = [...this.data];

          const existingItemsMap = new Map(
            updatedData.map((item: any, index: number) => [item.id, index])
          );

          newItems.forEach((newItem: any) => {
            const existingIndex = existingItemsMap.get(newItem.id);

            if (existingIndex !== undefined) {
              updatedData[existingIndex] = newItem;
            } else {
              updatedData.push(newItem);
            }
          });

          this.data = updatedData;
        }
      }
    });
  }

  get formatDate() {
    return (date: any) => moment(date).calendar()
  }

  playlistClosed(event: any) {
    this.showPlaylist = event;
  }

  playlistSelected(playlists: any) {
    this.showPlaylist = false;
    this.playlists = playlists;
  }

  likeTrack(item: any) {
    if (!this.authService.token()) {
      this.shareDataService.showInfoModal('error');
      return
    }
    this.audioService.like(item.id).subscribe((r) => {
      item.liked = !item.liked;
      if(item.liked) item.likes++
      else item.likes--;
      if ((r as any).audio) {
        this.toasterService.showToast('Лекция добавлена в понравившееся!', 'success', 'bottom-middle', 3000);
      } else {
        this.toasterService.showToast('Лекция удалена из понравившихся!', 'success', 'bottom-middle', 3000);
      }
    })
  }

  shareTrack(track: any) {
    navigator.clipboard.writeText('https://dev.advayta.org/' + this.router.url + '/' + track.external_id).then(() => {
      this.toasterService.showToast('Ссылка на аудио скопирована в буфер обмена!', 'success', 'bottom-middle', 3000)
    }).catch((error) => {
      console.error('Unable to copy text to clipboard', error);
    });
  }

  getLangElement(item: any) {
    return item.lang;
  }

  isInFavourites(id: number) {
    return this.favouriteContent.includes(id)
  }

  navigate(article: any) {
    this.router.navigate(['/' + this.translocoService.getActiveLang() + '/categories/1/' + article.slug])
  }

  addTagFilter(tag: any) {
    if (!this.selectedTags.some(t => t.id === tag.id)) {
      this.selectedTags.push(tag);
      this.updateUrlWithTags();
    }
  }

  updateUrlWithTags() {
    const queryParams: any = {};
    if (this.selectedTags.length > 0) {
      const tagIds = this.selectedTags.map(tag => tag.id);
      queryParams.tags = tagIds.join(',');
    } else {
      queryParams.tags = null;
    }
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: queryParams,
      queryParamsHandling: 'merge'
    });
  }

  ngOnInit() {
    this.lang = this.translocoService.getActiveLang();
    this.route.queryParams.subscribe(params => {
      if (params['q']) {
        this.query = params['q'];
        this.isInitialState = false;
        this.search();
      }
    });

    this.getPlaylists()
  }

  favorites(item: any) {
    if (!this.authService.isAuth) {
      this.shareDataService.showInfoModal('error');
      return
    }
    this.libraryService.addToFavourites(item.id).subscribe({
      next: (r) => {
        item.inFavourites = !item.inFavourites;
        if(item.inFavourites) {
          this.toasterService.showToast('Книга добавлена в избранное!', 'success', 'bottom-middle', 3000);
        } else {
          this.toasterService.showToast('Книга удалена из избранного!', 'success', 'bottom-middle', 3000);
        }
      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    })
  }

  showMenu(index: number, event: Event) {
    event.stopPropagation();

    this.show_menu = {};
    this.show_menu[index] = !this.show_menu[index];
  }

  search(clearItems = false) {
    if (!this.query) return;

    // Отмечаем, что это уже не начальное состояние
    this.isInitialState = false;
    this.isLoading = true;

    if(clearItems) this.items = [];

    const url = this.router.createUrlTree([], {
      relativeTo: this.route,
      queryParams: { q: this.query, page: this.page },
      queryParamsHandling: 'merge',
    }).toString();

    this.location.replaceState(url);

    this.searchService
      .search(this.query, this.page, this.activeTab)
      .subscribe({
        next: (res: any) => {
          const newItems = res.items;
          const existingIds = new Set(this.items.map((i: any) => i.id));
          const uniqueNewItems = newItems.filter((newItem: any) => !existingIds.has(newItem.id));
          this.items = [...this.items, ...uniqueNewItems];
          this.totalPages = res.pagination.totalPages;
          this.totalItems = res.pagination.total;

          // Если ничего не найдено, устанавливаем рандомное сообщение
          if (this.items.length === 0) {
            this.setRandomMessage(this.emptySearchMessages);
          }

          this.isLoading = false;
        },
        error: () => {
          this.isLoading = false;
        }
      })

    this.contentCache.clear();
  }

  // Helper method to check if current tab has any items
  hasItemsForCurrentTab(): boolean {
    if (!this.items || this.items.length === 0) return false;

    switch (this.activeTab) {
      case SearchTabTypes.ALL:
        return this.items.length > 0;
      case SearchTabTypes.CONTENT:
        return this.items.some((item: any) => item.type === 'content');
      case SearchTabTypes.LIBRARY:
        return this.items.some((item: any) => item.type === 'library');
      case SearchTabTypes.LECTURES:
        return this.items.some((item: any) => item.type === 'audio');
      case SearchTabTypes.PHOTO:
        return this.items.some((item: any) => item.type === 'photo');
      case SearchTabTypes.FORUM:
        return this.items.some((item: any) => item.type === 'forum');
      default:
        return false;
    }
  }

  // Helper method to get filtered items for current tab
  getItemsForCurrentTab(): any[] {
    if (!this.items || this.items.length === 0) return [];

    switch (this.activeTab) {
      case SearchTabTypes.ALL:
        return this.items;
      case SearchTabTypes.CONTENT:
        return this.items.filter((item: any) => item.type === 'content');
      case SearchTabTypes.LIBRARY:
        return this.items.filter((item: any) => item.type === 'library');
      case SearchTabTypes.LECTURES:
        return this.items.filter((item: any) => item.type === 'audio');
      case SearchTabTypes.PHOTO:
        return this.items.filter((item: any) => item.type === 'photo');
      case SearchTabTypes.FORUM:
        return this.items.filter((item: any) => item.type === 'forum');
      default:
        return [];
    }
  }
  private contentCache = new Map<string, string>();

  getContent(item: any) {
    // Create a unique key for the item
    const itemKey = item.id || JSON.stringify(item);

    // Return cached content if exists
    if (this.contentCache.has(itemKey)) {
      return this.contentCache.get(itemKey);
    }

    let content = '';
    const list = Object.keys(item);

    for (let key of list) {
      if (typeof item[key] !== 'string') continue;
      if (item[key].toLowerCase().indexOf(this.query!.toLowerCase()) > -1) {
        content = item[key];
        break;
      }
    }

    // Cache the processed content
    this.contentCache.set(itemKey, content);
    return content;
  }

  toggleSearchPanel() {
    this.searchShowChanged.emit(true);
  }

  followLink(item: any) {
    const lang = this.translocoService.getActiveLang();

    // For audio objects
    if (item.external_id && item.duration) {
      this.router.navigateByUrl(`/${lang}/audiogallery/audiolektsii/${item.external_id}`);
    }
    // For content objects
    else if (item.slug) {
      this.router.navigateByUrl(`/${lang}/test/${item.slug}`);
    }
    // For library objects
    else if (item.external_id) {
      this.router.navigate([`/${lang}/library/${item.code}`]);
    }
    else if (item.key == 'forum') {
      this.router.navigate([`/${lang}/forum/topic/${item.id}`]);
    }

    this.searchShowChanged.emit(true);
  }

  format(title: string) {
    if (!title) return '';
    if (this.query) {
      const regex = new RegExp(this.query, 'gi');
      return title.replace(regex, match =>
        `<span class="term-format">${match}</span>`
      );
    }
    return title;
  }

  sort(arr: any[]) {
    if (!this.query || !arr) return arr;
    const queryLower = this.query.toLowerCase();

    return arr.sort((a, b) => {
      const getRelevanceScore = (item: any) => {
        let score = 6; // Наименьший приоритет

        if (!item) return score;

        if (item?.title?.toLowerCase() === queryLower) return 1;

        if (item?.title?.toLowerCase().includes(queryLower)) return 2;

        if (item?.annotation?.toLowerCase().includes(queryLower)) return 3;
        if (item?.recomendation?.toLowerCase().includes(queryLower)) return 3;
        if (item?.description?.toLowerCase().includes(queryLower)) return 3;

        if (item?.content?.toLowerCase().includes(queryLower)) return 4;

        if (item?.tags?.some((tag: any) => tag.name?.toLowerCase() === queryLower)) return 5;

        return score;
      };

      const aScore = getRelevanceScore(a);
      const bScore = getRelevanceScore(b);

      return aScore - bScore;
    });
  }


  nextPage() {
    if (this.page < this.totalPages) {
      this.page++;
      this.search();
    }
  }

  likeArticle(item: any) {
    if (!this.authService.token()) {
      this.shareDataService.showInfoModal('error');
      return;
    }

    this.contentService.like(item.id).subscribe({
      next: (r) => {
        item.liked = !item.liked;
        if(item.liked) item.likes++
        else item.likes--;

        if (!r) {
          this.toasterService.showToast('Статья добавлена в понравившееся!', 'success', 'bottom-middle', 3000);
        } else {
          this.toasterService.showToast('Статья удалена из понравившихся!', 'success', 'bottom-middle', 3000);
        }
      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    });
  }

  favoritesArticle(item: any) {
    if (!this.authService.token()) {
      this.shareDataService.showInfoModal('error');
      return
    }
    this.contentService.addToFavourites(item.id).subscribe({
      next: (r) => {
        item.inFavourites = !item.inFavourites;
        if(item.inFavourites) {
          this.toasterService.showToast('Статья добавлена в избранное!', 'success', 'bottom-middle', 3000);
        } else {
          this.toasterService.showToast('Статья удалена из избранного!', 'success', 'bottom-middle', 3000);
        }
      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    })
  }

  addToPlaylist(track: Track) {
    this.shareDataService.addToPlaylist(track);
    this.toasterService.showToast('Лекция добавлена в очередь!', 'success', 'bottom-middle', 3000);
  }

  showPlaylistDialog(id: any) {
    if (!this.authService.token()) {
      this.shareDataService.showInfoModal('error');
      return
    }
    this.showPlaylist = true;
    this.selectedTrackId = id;
  }

  playTrack(track: Track) {
    this.shareDataService.addToPlaylist(track, true);
  }

  openLecture(key: string) {
    this.router.navigate([this.translocoService.getActiveLang(), 'audiogallery', 'audiolektsii', key]);
  }

  photoRedirect(item: any) {

  }

  favoritePhoto(item: any) {
    if (!this.authService.token()) {
      this.shareDataService.showInfoModal('error');
      return
    }
    this.photoService.addToFavourites(item.id).subscribe({
      next: (r) => {
        item.inFavourites = !item.inFavourites;
        if(!item.inFavourites) {
          this.toasterService.showToast('Фото удалено из избранного!', 'success', 'bottom-middle', 3000);
        } else {
          this.toasterService.showToast('Фото добавленo в избранное!', 'success', 'bottom-middle', 3000);
        }
      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    })
  }

  favoriteTopic(topic: any) {
    this.forumService.favoriteTopic(topic.id).subscribe({
      next: () => {
        topic.inFavourites = !topic.inFavourites;
        if(!topic.inFavourites) {
          this.toasterService.showToast('Форум удален из избранного!', 'success', 'bottom-middle', 3000);
        } else {
          this.toasterService.showToast('Форум добавлен в избранное!', 'success', 'bottom-middle', 3000);
        }
      },
      error: () => {

      }
    });
  }

  share(item: any) {

  }

  shareArticle(content: any) {
    if (isPlatformBrowser(this.platformId)) {
      const lang = this.translocoService.getActiveLang()
      navigator.clipboard.writeText(`${environment.baseUrl}/${lang}/test/${content.slug}`).then(() =>
        this.toasterService.showToast('Ссылка на страницу скопирована в буфер обмена!', 'success', 'bottom-middle', 3000)
      )
    }
  }

  likeLibrary(item: any) {
    if (!this.authService.isAuth) {
      this.shareDataService.showInfoModal('error');
      return;
    }

    this.libraryService.like(item.id).subscribe({
      next: (r) => {
        item.liked = !item.liked;
        if(item.liked) item.likes++
        else item.likes--;
      },
      error: err => {
        this.toasterService.showToast(err.message, 'error', 'bottom-middle');
      }
    });
  }

  showNext() {
    const currentIndex = this.tabs.findIndex(tab => tab === this.activeTab);
    if (currentIndex < this.tabs.length - 1) {
      this.activeTab = this.tabs[currentIndex + 1];
      this.page = 1;
      this.search(true);
    }
  }

  showPrev() {
    const currentIndex = this.tabs.findIndex(tab => tab === this.activeTab);
    if (currentIndex > 0) {
      this.activeTab = this.tabs[currentIndex - 1];
      this.page = 1;
      this.search(true);
    }
  }

  closeMobileActionSelect() {
    this.selectedDropdElement = null;
  }

  showMobileActionOptions(quote: any) {
    this.selectedDropdElement = quote;
  }

  onClickMobileAction() {

    this.closeMobileActionSelect();
  }

  isLiked(item: any) {

  }

  likePhoto(item: any) {
    if (!this.authService.token()) {
      this.shareDataService.showInfoModal('error');
      return;
    }

    this.photoService.like(item.id).subscribe(() => {
      item.liked = !item.liked;
      if(item.liked) item.likes++;
      else item.likes--;
    })
  }

  likeTopic(topic: any) {
    this.forumService.likeTopic(topic.id).subscribe(() => {
      topic.liked = !topic.liked;
      if(!topic.liked) {
        topic.likes--
      } else {
        topic.likes++
      }
    })
  }

  copyTopic(item: any) {
    let url = this.environment.baseUrl + '/ru/forum/topic/' + item;
    navigator.clipboard.writeText(url)
      .then(() => {
        this.toasterService.showToast('Текст скопирован в буфер обмена!', 'success', 'bottom-middle', 3000);
      })
      .catch(err => {
        console.error('Не удалось скопировать текст: ', err);
        this.toasterService.showToast('Не удалось скопировать текст', 'error', 'bottom-middle', 3000);
      });
  }

  getPlaylists() {
    if(this.authService.isAuth) {
      this.profileService.getPlaylist().subscribe((res: any) => this.playlists = res);
    }
  }

  // Метод для установки рандомного сообщения
  setRandomMessage(messages: string[]) {
    const randomIndex = Math.floor(Math.random() * messages.length);
    this.currentRandomMessage = messages[randomIndex];
  }

}
