import { Component, ElementRef, ViewChild, inject, PLATFORM_ID, OnInit, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule, isPlatformBrowser, NgOptimizedImage } from '@angular/common';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { Subject, takeUntil } from 'rxjs';
import { AdDisplayService, Advertisement } from '../../services/ad-display.service';
import { environment } from '@/env/environment';

@Component({
  selector: 'app-ad-dialog',
  standalone: true,
  imports: [CommonModule, NgOptimizedImage],
  changeDetection: ChangeDetectionStrategy.OnPush,
  templateUrl: './ad-dialog.component.html',
  styleUrls: ['./ad-dialog.component.scss']
})
export class AdDialogComponent implements OnInit, OnDestroy {
  @ViewChild('adDialog') adDialog!: ElementRef<HTMLDialogElement>;
  
  private platformId = inject(PLATFORM_ID);
  private sanitizer = inject(DomSanitizer);
  private adDisplayService = inject(AdDisplayService);
  private cdr = inject(ChangeDetectorRef);
  private destroy$ = new Subject<void>();
  
  environment = environment;
  currentAd: Advertisement | null = null;
  safeAdLink: SafeResourceUrl | null = null;
  isClosing = false;
  showContent = false;

  ngOnInit(): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    // Listen for ads to display
    this.adDisplayService.showAd$
      .pipe(takeUntil(this.destroy$))
      .subscribe(ad => {
        console.log('AdDialogComponent received ad:', ad);
        if (ad && !this.isClosing && !this.showContent) {
          this.showAd(ad);
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Show the advertisement dialog
   */
  private showAd(ad: Advertisement): void {
    console.log('AdDialogComponent showAd called with:', ad);
    this.currentAd = ad;
    this.isClosing = false;
    this.showContent = true;

    // Prepare safe link for display
    this.safeAdLink = this.sanitizer.bypassSecurityTrustResourceUrl(ad.link);

    // Trigger change detection to update the template
    this.cdr.detectChanges();

    console.log('AdDialogComponent currentAd set to:', this.currentAd);

    // Use setTimeout to ensure the ViewChild is properly initialized and DOM is updated
    setTimeout(() => {
      // Show the dialog
      if (this.adDialog && isPlatformBrowser(this.platformId)) {
        console.log('AdDialogComponent showing modal dialog');
        const dialog = this.adDialog.nativeElement;
        dialog.showModal();

        // Add opening animation class
        dialog.classList.add('ad-dialog-opening');

        // Remove opening class after animation
        setTimeout(() => {
          dialog.classList.remove('ad-dialog-opening');
        }, 300);
      } else {
        console.warn('AdDialogComponent: adDialog ViewChild not available or not in browser');
      }
    }, 0);
  }

  /**
   * Close the ad dialog
   */
  closeAdDialog(): void {
    if (this.isClosing || !this.adDialog || !this.showContent) {
      return;
    }

    console.log('AdDialogComponent: Starting close animation');
    this.isClosing = true;
    const dialog = this.adDialog.nativeElement;

    console.log('AdDialogComponent: showContent before close:', this.showContent);

    // Add closing animation class
    dialog.classList.add('ad-dialog-closing');

    // Wait for animation to complete before actually closing
    setTimeout(() => {
      console.log('AdDialogComponent: Closing modal after animation');

      // Close the dialog first
      dialog.close();
      dialog.classList.remove('ad-dialog-closing');

      // Clear data and flags after dialog is closed
      this.currentAd = null;
      this.safeAdLink = null;
      this.isClosing = false;
      this.showContent = false;

      // Notify the service that modal is closed
      console.log('AdDialogComponent: Notifying service that modal is closed');
      this.adDisplayService.notifyModalClosed();

      // Trigger change detection to update the template
      this.cdr.detectChanges();
    }, 250); // Animation duration
  }

  /**
   * Navigate to the ad link and close dialog
   */
  navigateToAdLink(): void {
    if (isPlatformBrowser(this.platformId) && this.currentAd) {
      window.open(this.currentAd.link, '_blank');
      this.closeAdDialog();
    }
  }

  /**
   * Handle clicking on the backdrop (outside the modal content)
   */
  onBackdropClick(event: MouseEvent): void {
    // Only close if clicking directly on the dialog element (backdrop) and content is visible
    if (event.target === this.adDialog?.nativeElement && this.showContent && !this.isClosing) {
      this.closeAdDialog();
    }
  }
}
