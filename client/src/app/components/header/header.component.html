<header [ngClass]="{'sidebar_open': isSidebarOpen}" class="flex flex-col">
    <div class="menu_wrap">
        <div class="m_grid nav_wrap">
            <div class="header">
                <div class="flex divide_">
                    <div class="burger cursor-pointer" (click)="toggleSidebar();$event.stopPropagation();"></div>
                    <div [routerLink]="['/']" class="center_logo"></div>
                    <a class="absolute close_btn hesd_" (click)="toggleSidebar();"><img
                            ngSrc="assets/images/close_btn.svg" width="34" height="33" loading="lazy" alt="x"></a>
                    <nav class="navbar">
                        <ul>
                            <li class="cursor-pointer"><a href="#">События</a></li>
                            <li class="cursor-pointer"><a  (click)="navigateToCatigory()">Курсы</a></li>
                            <li class="cursor-pointer"><a (click)="navigateToLibrary()">Библиотека</a></li>

                            <li class="cursor-pointer"><a (click)="navigateToAudio()">Медиа</a></li>
                            <li class="cursor-pointer"><a href="#">Традиция</a></li>
                            <li class="cursor-pointer"><a href="#">Практика</a></li>
                            <li class="cursor-pointer item"><a (click)="navigateToForum();">Форум</a></li>
                            <li class="cursor-pointer item serch"><a class="rout_l" [routerLink]="['/', currentLanguage(), 'search']"></a></li>
                            <!-- <li class="cursor-pointer"><a href="#"
                                    (click)="$event.stopPropagation(); $event.preventDefault(); router.navigate(['/ru/categories'])">Категории</a>
                            </li> -->
                            <li (click)="router.navigate(['/ru/donation'])" class="cursor-pointer item button_img_before relative"><a
                                    href="#"><button>Пожертвовать</button></a></li>
                        </ul>
                    </nav>
                    <div [routerLink]="['/', currentLanguage(), 'search']" class="nav_cont_sch"></div>
                    <div class="user-menu-container" [ngClass]="{'hide': !isSidebarOpen}">
                        @if (profileService.isAuth) {
                        <!-- Авторизованный пользователь -->
                        <div class="user-menu auth_ relative" (click)="toggleUserMenu($event)" appClickOutside (clickOutside)="closeUserMenu()">
                            <div class="relative cursor-pointer">
                                <div class="notifications">
                                    <div class="user_logo_ authorized_">
                                        @if (profileService.profile && profileService.profile.avatar && profileService.profile.avatar.name) {
                                        <img ngSrc="{{environment.serverUrl + '/upload/' + profileService.avatar()}}" width="36" height="36" loading="lazy" alt="logo">
                                        } @else {
                                        <img ngSrc="assets/images/user_logo.webp" width="36" height="36" loading="lazy" alt="logo">
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="user-dropdown has-arrow">{{ profileService.name() === "null" ? '' : profileService.name() }}</div>

                            <!-- Выпадающее меню -->
                            @if (isUserMenuOpen()) {
                            <div class="user-dropdown-menu">
                                <div class="dropdown-item" (click)="navigateToProfile();$event.stopPropagation();">
                                    <span>Профиль</span>
                                </div>
                                <div class="dropdown-item" (click)="logout();$event.stopPropagation();">
                                    <span>Выйти</span>
                                </div>
                            </div>
                            }
                        </div>
                        } @else {
                        <!-- Неавторизованный пользователь -->
                        <a [routerLink]="['/ru/signin']" class="user-menu auth_">
                            <div class="relative cursor-pointer">
                                <div class="notifications">
                                    <div class="user_logo_"></div>
                                </div>
                            </div>
                            <div class="user-dropdown">Войти</div>
                        </a>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>
