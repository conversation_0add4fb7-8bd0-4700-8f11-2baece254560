<div class="forum-favorite flex-col">
  @for(topic of forumFavouritePagination; track topic.id) {
      <div class="flex forum-item justify-between gap-2 w-full">
        <div class="forum-main-content justify-between gap-[40px] w-full">
          <div class="flex">
            <div class="flex flex-col gap-2">
              <a [routerLink]="'/ru/forum/topic/' + (topic?.topicComment ? topic?.topicComment?.topic.id : topic?.topic?.id)" class="forum-name">
                {{topic?.topicComment?.comment || topic?.topic?.name}}
              </a>
              <div class="forum-type">
                {{topic?.topicComment?.topic?.category?.name || topic.topic?.category?.name}}
                <span *ngIf="topic?.topicComment"> / {{topic?.topicComment?.topic?.name}}</span>
              </div>
              <div class="forum-last-answer pt-3" *ngIf="!topic?.topicComment && topic?.topic?.comments.length">
                Последний ответ: <span>{{formatDate(topic?.topic?.comments[topic?.topic?.comments.length-1].createdAt)}} от {{topic?.topic?.comments[topic?.topic?.comments.length-1]?.user?.firstName}}</span>
              </div>
            </div>
          </div>
          <div class="flex justify-between forum-item-right-content gap-3">
            <button class="flex items-center icons_w lik_hov" [ngClass]="{'is-liked': topic?.topicComment?.liked || topic?.topic?.liked}"
                    (click)="like(topic); $event.stopPropagation();">
              <div class="icon-wrap like_w">
                <svg class="emty_l" width="24" height="22" viewBox="0 0 24 22" fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                    fill="var(--font-color1)" />
                </svg>
                <svg class="emty_l_hover" width="24" height="22" viewBox="0 0 24 22" fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                    fill="var(--text-color)" />
                </svg>
              </div>
              <span class="ml-2 text-color default_" *ngIf="topic?.topicComment">{{topic?.topicComment?.likes}}</span>
              <span class="ml-2 text-color default_" *ngIf="topic?.topic">{{topic?.topic?.likes}}</span>
              <div class="on_hov">
                мне нравится
              </div>
            </button>
            <div class="actions_w flex gap-[20px] items-center">
              <div class="flex items-center cursor-pointer icons_w fav_hov"
                   (click)="!topic?.topicComment ? favoriteTopic(topic?.topic?.id) : favoriteComment(topic?.topicComment?.id)">
                <div class="icon-wrap star_w">
                  <svg class="emty_f" width="18" height="22" viewBox="0 0 18 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10.6234 0C10.7094 0.0286963 10.7955 0.0573925 10.8816 0.0803496C11.7655 0.338616 12.3624 1.11342 12.3796 2.0317C12.3853 2.26127 12.3796 2.49084 12.3796 2.7491C12.4657 2.7491 12.5403 2.7491 12.6149 2.7491C13.7685 2.7491 14.9221 2.7491 16.0699 2.7491C17.1432 2.7491 17.872 3.48373 17.872 4.55697C17.872 5.29159 17.872 6.03196 17.872 6.76658C17.872 7.28885 17.608 7.55286 17.0743 7.5586C16.9882 7.5586 16.9078 7.5586 16.8103 7.5586C16.7758 8.24731 16.7471 8.90732 16.7127 9.57308C16.6037 11.863 16.4946 14.1473 16.3856 16.4372C16.3282 17.6654 16.2823 18.8994 16.2019 20.1276C16.133 21.1664 15.2607 21.9699 14.2219 21.9928C13.7398 22.0043 13.2634 21.9986 12.7813 21.9986C9.79117 21.9986 6.79528 21.9986 3.80513 21.9986C2.72041 21.9986 1.85952 21.3213 1.69308 20.2997C1.62421 19.8578 1.62421 19.3987 1.60125 18.951C1.49221 16.7012 1.3889 14.4457 1.28559 12.1959C1.21672 10.6865 1.14211 9.17707 1.0675 7.66764C1.0675 7.63895 1.05602 7.61599 1.05028 7.56434C0.92402 7.56434 0.809235 7.57008 0.688711 7.56434C0.298441 7.55286 0.00573925 7.27164 0 6.88137C0.00573925 6.04917 0 5.21698 0.0172178 4.39053C0.0344355 3.48947 0.792017 2.76058 1.7103 2.75484C2.89258 2.74336 4.07487 2.75484 5.25142 2.75484C5.32603 2.75484 5.40638 2.75484 5.50968 2.75484C5.50968 2.54249 5.50968 2.34162 5.50968 2.14648C5.51542 1.06176 6.14674 0.275484 7.19703 0.0344355C7.22572 0.0286963 7.24868 0.0114785 7.27164 0C8.39079 0 9.50421 0 10.6234 0ZM2.47362 7.57008C2.47362 7.77095 2.46788 7.94887 2.47362 8.12679C2.58266 10.4225 2.69171 12.7182 2.80076 15.0082C2.87537 16.6151 2.94998 18.2279 3.03033 19.8349C3.05902 20.3858 3.31155 20.6154 3.86252 20.6154C7.24868 20.6154 10.6348 20.6154 14.021 20.6154C14.5834 20.6154 14.8302 20.3858 14.8589 19.8176C14.9508 17.8893 15.0426 15.9551 15.1344 14.0267C15.2148 12.3624 15.2951 10.698 15.3755 9.02785C15.3984 8.54575 15.4214 8.05791 15.4386 7.56434C11.1055 7.57008 6.79528 7.57008 2.47362 7.57008ZM16.5061 6.18118C16.5061 5.65891 16.5061 5.15959 16.5061 4.66027C16.5061 4.21835 16.42 4.13226 15.9838 4.13226C11.2891 4.13226 6.5944 4.13226 1.89969 4.13226C1.85952 4.13226 1.8136 4.13226 1.77343 4.13226C1.5209 4.138 1.3889 4.26427 1.3889 4.51679C1.38316 5.02759 1.3889 5.53264 1.3889 6.04344C1.3889 6.08935 1.40038 6.13526 1.40612 6.18692C6.43944 6.18118 11.4613 6.18118 16.5061 6.18118ZM11.0079 2.74336C11.0079 2.4851 11.0194 2.24979 11.0079 2.01448C10.9907 1.66438 10.7037 1.38316 10.3479 1.37742C9.41812 1.37168 8.48836 1.37168 7.5586 1.37742C7.23146 1.37742 6.93876 1.62995 6.90432 1.95135C6.87563 2.20961 6.89858 2.47362 6.89858 2.74336C8.27027 2.74336 9.62473 2.74336 11.0079 2.74336Z" fill="var(--font-color1)"/>
                    <path d="M6.20236 14.1013C6.20236 15.5763 6.20236 17.0513 6.20236 18.5263C6.20236 19.0371 5.73748 19.3757 5.28408 19.2035C5.01433 19.1002 4.83642 18.8592 4.83068 18.5607C4.82494 18.2451 4.83068 17.9294 4.83068 17.6138C4.83068 14.9737 4.83068 12.3279 4.83068 9.68784C4.83068 9.14262 5.28982 8.79826 5.76043 8.98192C6.0474 9.09096 6.20236 9.34349 6.20236 9.69932C6.20236 11.1628 6.20236 12.6321 6.20236 14.1013Z" fill="var(--font-color1)"/>
                    <path d="M9.63986 14.0841C9.63986 15.5591 9.63986 17.0341 9.63986 18.5091C9.63986 19.0371 9.17498 19.3815 8.71584 19.2036C8.44035 19.1003 8.26818 18.8535 8.26818 18.5378C8.26244 17.9811 8.26818 17.4187 8.26818 16.8619C8.26818 14.4687 8.26818 12.0811 8.26818 9.68788C8.26818 9.14839 8.72732 8.80403 9.20367 8.98769C9.47916 9.09674 9.63986 9.34352 9.63986 9.68214C9.63986 11.0136 9.63986 12.3452 9.63986 13.6767C9.63986 13.8087 9.63986 13.9464 9.63986 14.0841Z" fill="var(--font-color1)"/>
                    <path d="M13.0674 14.1128C13.0674 15.5821 13.0674 17.0456 13.0674 18.5148C13.0674 18.8247 12.9354 19.0543 12.6542 19.1863C12.3844 19.3068 12.1376 19.2552 11.9195 19.06C11.7474 18.9051 11.69 18.7099 11.6957 18.4804C11.7014 17.2866 11.6957 16.0871 11.6957 14.8933C11.6957 13.1543 11.6957 11.4154 11.6957 9.67636C11.6957 9.13687 12.1721 8.79825 12.6369 8.98765C12.9124 9.09669 13.0674 9.34922 13.0674 9.69358C13.0731 11.1628 13.0674 12.6378 13.0674 14.1128Z" fill="var(--font-color1)"/>
                  </svg>
                  <svg class="emty_f_hover" width="18" height="22" viewBox="0 0 18 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10.6234 0C10.7094 0.0286963 10.7955 0.0573925 10.8816 0.0803496C11.7655 0.338616 12.3624 1.11342 12.3796 2.0317C12.3853 2.26127 12.3796 2.49084 12.3796 2.7491C12.4657 2.7491 12.5403 2.7491 12.6149 2.7491C13.7685 2.7491 14.9221 2.7491 16.0699 2.7491C17.1432 2.7491 17.872 3.48373 17.872 4.55697C17.872 5.29159 17.872 6.03196 17.872 6.76658C17.872 7.28885 17.608 7.55286 17.0743 7.5586C16.9882 7.5586 16.9078 7.5586 16.8103 7.5586C16.7758 8.24731 16.7471 8.90732 16.7127 9.57308C16.6037 11.863 16.4946 14.1473 16.3856 16.4372C16.3282 17.6654 16.2823 18.8994 16.2019 20.1276C16.133 21.1664 15.2607 21.9699 14.2219 21.9928C13.7398 22.0043 13.2634 21.9986 12.7813 21.9986C9.79117 21.9986 6.79528 21.9986 3.80513 21.9986C2.72041 21.9986 1.85952 21.3213 1.69308 20.2997C1.62421 19.8578 1.62421 19.3987 1.60125 18.951C1.49221 16.7012 1.3889 14.4457 1.28559 12.1959C1.21672 10.6865 1.14211 9.17707 1.0675 7.66764C1.0675 7.63895 1.05602 7.61599 1.05028 7.56434C0.92402 7.56434 0.809235 7.57008 0.688711 7.56434C0.298441 7.55286 0.00573925 7.27164 0 6.88137C0.00573925 6.04917 0 5.21698 0.0172178 4.39053C0.0344355 3.48947 0.792017 2.76058 1.7103 2.75484C2.89258 2.74336 4.07487 2.75484 5.25142 2.75484C5.32603 2.75484 5.40638 2.75484 5.50968 2.75484C5.50968 2.54249 5.50968 2.34162 5.50968 2.14648C5.51542 1.06176 6.14674 0.275484 7.19703 0.0344355C7.22572 0.0286963 7.24868 0.0114785 7.27164 0C8.39079 0 9.50421 0 10.6234 0ZM2.47362 7.57008C2.47362 7.77095 2.46788 7.94887 2.47362 8.12679C2.58266 10.4225 2.69171 12.7182 2.80076 15.0082C2.87537 16.6151 2.94998 18.2279 3.03033 19.8349C3.05902 20.3858 3.31155 20.6154 3.86252 20.6154C7.24868 20.6154 10.6348 20.6154 14.021 20.6154C14.5834 20.6154 14.8302 20.3858 14.8589 19.8176C14.9508 17.8893 15.0426 15.9551 15.1344 14.0267C15.2148 12.3624 15.2951 10.698 15.3755 9.02785C15.3984 8.54575 15.4214 8.05791 15.4386 7.56434C11.1055 7.57008 6.79528 7.57008 2.47362 7.57008ZM16.5061 6.18118C16.5061 5.65891 16.5061 5.15959 16.5061 4.66027C16.5061 4.21835 16.42 4.13226 15.9838 4.13226C11.2891 4.13226 6.5944 4.13226 1.89969 4.13226C1.85952 4.13226 1.8136 4.13226 1.77343 4.13226C1.5209 4.138 1.3889 4.26427 1.3889 4.51679C1.38316 5.02759 1.3889 5.53264 1.3889 6.04344C1.3889 6.08935 1.40038 6.13526 1.40612 6.18692C6.43944 6.18118 11.4613 6.18118 16.5061 6.18118ZM11.0079 2.74336C11.0079 2.4851 11.0194 2.24979 11.0079 2.01448C10.9907 1.66438 10.7037 1.38316 10.3479 1.37742C9.41812 1.37168 8.48836 1.37168 7.5586 1.37742C7.23146 1.37742 6.93876 1.62995 6.90432 1.95135C6.87563 2.20961 6.89858 2.47362 6.89858 2.74336C8.27027 2.74336 9.62473 2.74336 11.0079 2.74336Z" fill="var(--text-color)"/>
                    <path d="M6.20236 14.1013C6.20236 15.5763 6.20236 17.0513 6.20236 18.5263C6.20236 19.0371 5.73748 19.3757 5.28408 19.2035C5.01433 19.1002 4.83642 18.8592 4.83068 18.5607C4.82494 18.2451 4.83068 17.9294 4.83068 17.6138C4.83068 14.9737 4.83068 12.3279 4.83068 9.68784C4.83068 9.14262 5.28982 8.79826 5.76043 8.98192C6.0474 9.09096 6.20236 9.34349 6.20236 9.69932C6.20236 11.1628 6.20236 12.6321 6.20236 14.1013Z" fill="var(--text-color)"/>
                    <path d="M9.63986 14.0841C9.63986 15.5591 9.63986 17.0341 9.63986 18.5091C9.63986 19.0371 9.17498 19.3815 8.71584 19.2036C8.44035 19.1003 8.26818 18.8535 8.26818 18.5378C8.26244 17.9811 8.26818 17.4187 8.26818 16.8619C8.26818 14.4687 8.26818 12.0811 8.26818 9.68788C8.26818 9.14839 8.72732 8.80403 9.20367 8.98769C9.47916 9.09674 9.63986 9.34352 9.63986 9.68214C9.63986 11.0136 9.63986 12.3452 9.63986 13.6767C9.63986 13.8087 9.63986 13.9464 9.63986 14.0841Z" fill="var(--text-color)"/>
                    <path d="M13.0674 14.1128C13.0674 15.5821 13.0674 17.0456 13.0674 18.5148C13.0674 18.8247 12.9354 19.0543 12.6542 19.1863C12.3844 19.3068 12.1376 19.2552 11.9195 19.06C11.7474 18.9051 11.69 18.7099 11.6957 18.4804C11.7014 17.2866 11.6957 16.0871 11.6957 14.8933C11.6957 13.1543 11.6957 11.4154 11.6957 9.67636C11.6957 9.13687 12.1721 8.79825 12.6369 8.98765C12.9124 9.09669 13.0674 9.34922 13.0674 9.69358C13.0731 11.1628 13.0674 12.6378 13.0674 14.1128Z" fill="var(--text-color)"/>
                  </svg>
                </div>
                <div class="on_hov">
                  удалить
                </div>
              </div>
              <div (click)="copyTopic(topic?.topicComment ? topic?.topicComment?.topic.id : topic?.topic?.id, topic?.topicComment?.id)" class="flex items-center cursor-pointer icons_w shr_hov" (click)="share(topic)">
                <div class="icon-wrap share_w">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M15.9238 7.01009C13.5066 8.21941 11.0965 9.42157 8.66504 10.638C8.9368 11.5325 8.94395 12.427 8.66504 13.3357C11.0822 14.5451 13.4923 15.7544 15.8737 16.9422C16.2456 16.5988 16.5745 16.2267 16.9607 15.9476C19.6353 13.9941 23.4542 15.6113 23.9334 18.8957C24.3481 21.7509 22.0168 24.2268 19.1347 23.9835C17.0894 23.8117 15.3731 22.1516 15.1514 20.1051C15.1085 19.6829 15.1156 19.2464 15.1729 18.8313C15.2086 18.588 15.1442 18.5093 14.9512 18.4163C12.8772 17.3859 10.8105 16.3483 8.73656 15.3107C8.50056 15.1891 8.25741 15.0817 8.02141 14.9458C7.87838 14.8671 7.79972 14.8885 7.6853 15.0102C6.76276 15.9834 5.63283 16.4556 4.28836 16.4413C2.21445 16.4127 0.326467 14.7454 0.0475605 12.6846C-0.288557 10.2588 1.18464 8.12638 3.58752 7.63979C5.16084 7.32494 6.52676 7.79722 7.65669 8.94213C7.80687 9.09241 7.89984 9.10672 8.07862 9.01369C10.3742 7.85447 12.677 6.70955 14.9798 5.56464C15.1585 5.47877 15.2158 5.40005 15.1729 5.18538C14.7295 2.75959 16.4458 0.39105 18.8773 0.047575C21.4232 -0.317367 23.7117 1.45725 23.9763 3.99753C24.2266 6.37323 22.4673 8.57004 20.093 8.8348C18.4196 9.028 17.0751 8.41977 16.0239 7.12458C15.9881 7.09596 15.9595 7.06018 15.9238 7.01009ZM7.14179 11.9976C7.14179 10.5021 5.91174 9.26414 4.42424 9.2713C2.94389 9.27845 1.721 10.4949 1.70669 11.9762C1.69239 13.4574 2.92244 14.7025 4.41709 14.7168C5.90459 14.7311 7.14179 13.5003 7.14179 11.9976ZM16.832 19.5541C16.8248 21.0496 18.0549 22.2876 19.5424 22.2804C21.0227 22.2804 22.2671 21.0353 22.2671 19.5541C22.2599 18.0728 21.0513 16.8635 19.5567 16.8492C18.062 16.8277 16.8391 18.0442 16.832 19.5541ZM19.5424 7.14605C21.037 7.14605 22.2671 5.91527 22.2671 4.42688C22.2599 2.93849 21.0156 1.70055 19.5352 1.7077C18.0477 1.71486 16.8463 2.93133 16.832 4.41972C16.8248 5.92242 18.0406 7.14605 19.5424 7.14605Z"
                      fill="var(--font-color1)" />
                  </svg>
                </div>
                <div class="on_hov">
                  поделиться
                </div>
              </div>
            </div>
            <div class="forum-answers-count flex justify-end">
              <span *ngIf="topic?.topic?.comments">{{topic?.topic?.comments.length}} ответ</span>
            </div>
          </div>
        </div>
        <div class="mobile-actions icons_w" (mouseleave)="closeMobileActionSelect()">
          <div class="icon-wrap star_w" (click)="showMobileActionOptions(topic)">
            <svg class="emty_f" width="7" height="22" viewBox="0 0 7 22" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="3.27778" cy="3.27778" r="2.27778" stroke="var(--font-color1)"/>
              <circle cx="3.27778" cy="11.2499" r="2.27778" stroke="var(--font-color1)"/>
              <circle cx="3.27778" cy="19.2221" r="2.27778" stroke="var(--font-color1)"/>
            </svg>
            <svg class="emty_f_hover" width="7" height="22" viewBox="0 0 7 22" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="3.27778" cy="3.27778" r="2.27778" fill="var(--text-color)" stroke="var(--text-color)"/>
              <circle cx="3.27778" cy="11.2499" r="2.27778" fill="var(--text-color)" stroke="var(--text-color)"/>
              <circle cx="3.27778" cy="19.2221" r="2.27778" fill="var(--text-color)" stroke="var(--text-color)"/>
            </svg>
          </div>
          @if (selectedDropdElement?.id === topic?.id) {
            <div class="dropdown-content-wrapper">
              <div class="dropdown-content">
                <div class="dropdown-content-inner">
                  @for(action of forumActions; track action) {
                    <div class="dropdown-item flex gap-2 items-center" (click)="onClickMobileAction()">
                      <ng-container *ngTemplateOutlet="dropdItemIcon; context: {action: action, item: topic}"></ng-container>
                      {{action}}
                    </div>
                  }
                </div>
              </div>
            </div>
          }
        </div>
      </div>
  }
</div>

<div class="buttn_catg" *ngIf="page < totalPages && items.length">
  <button class="load-more-button" (click)="page = page + 1" [disabled]="false">
    <span>Загрузить еще</span>
  </button>
</div>

<dialog class="stylized_wide fixed" #confirmDialog>
  <div class="dialog-message">
    {{ message }}
  </div>
  <div class="mt-[60px] flex gap-[16px] text-center justify-center dialog-footer">
    <button type="submit" class="confirm-btn ok-button">
      <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
      <div class="confirm-btn-label">Да</div>
    </button>
    <button type="submit" class="confirm-btn cancel-button">
      <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
      <div class="confirm-btn-label">Отмена</div>
    </button>
  </div>
</dialog>

<dialog #modal>
  <div>
    {{message}}
  </div>
  <div class="text-center mt-6">
    <button (click)="closeModal(modal)" class="cancel-button">Да</button>
  </div>
</dialog>


<ng-template #dropdItemIcon let-action="action">
  <ng-container [ngSwitch]="action">
    <div *ngSwitchCase="'копировать'">
      <svg width="16" height="15" viewBox="0 0 22 22" xmlns="http://www.w3.org/2000/svg">
        <path d="M8.4248 5.16141C6.49927 5.16141 4.57374 5.15626 2.64821 5.16141C1.36109 5.16655 0.336546 5.97446 0.0636768 7.20434C0.0173397 7.41017 0.0018959 7.6263 0.0018959 7.84243C-0.00325394 11.6658 -0.00325394 15.4944 0.0018959 19.3178C0.0018959 20.8822 1.12426 22.004 2.68425 22.004C6.50957 22.004 10.34 22.004 14.1653 22.004C15.715 22.004 16.8477 20.8873 16.8477 19.3384C16.8528 15.5047 16.8528 11.671 16.8477 7.83728C16.8477 6.27292 15.7253 5.16141 14.1499 5.16141C12.245 5.15626 10.3349 5.16141 8.4248 5.16141ZM15.123 13.5801C15.123 15.479 15.123 17.383 15.123 19.2818C15.123 19.9559 14.8038 20.275 14.1293 20.275C10.3246 20.275 6.52501 20.275 2.72029 20.275C2.04584 20.275 1.72663 19.9559 1.72663 19.2767C1.72663 15.4738 1.72663 11.6761 1.72663 7.8733C1.72663 7.19919 2.04584 6.88014 2.72544 6.88014C6.53016 6.88014 10.3297 6.88014 14.1345 6.88014C14.8141 6.88014 15.1281 7.19919 15.1281 7.87845C15.123 9.78244 15.123 11.6813 15.123 13.5801Z" fill="var(--font-color1)"/>
        <path d="M5.18795 6.05114C5.74398 6.05114 6.28972 6.05114 6.87665 6.05114C6.87665 5.82471 6.87665 2.94349 6.87665 2.72221C6.87665 2.07383 7.21644 1.72905 7.86 1.72905C11.6699 1.72905 15.4849 1.72905 19.2948 1.72905C19.9435 1.7239 20.2884 2.06353 20.2884 2.70677C20.2884 6.51475 20.2884 10.3279 20.2884 14.1359C20.2884 14.7842 19.9435 15.129 19.2999 15.129C19.0631 15.129 16.7555 15.129 16.5033 15.129C16.5033 15.7002 16.5033 16.2508 16.5033 16.8066C16.5555 16.8147 18.6783 16.8219 18.7299 16.828C20.348 17.0213 21.6675 16.2614 21.9668 14.6607C21.9926 14.5269 21.9977 14.388 21.9977 14.2491C21.9977 10.369 22.0029 6.49417 21.9977 2.61415C21.9977 1.2865 21.1122 0.267609 19.8148 0.0411882C19.65 0.0154586 19.4801 0.0103127 19.3102 0.0103127C15.49 0.0103127 11.6699 0.0206045 7.85486 2.08741e-05C6.48021 -0.00512504 5.363 0.941724 5.1931 2.21791C5.14676 2.59871 5.18795 5.63946 5.18795 6.05114Z" fill="var(--font-color1)"/>
      </svg>
    </div>
    <div *ngSwitchCase="'удалить'">
      <svg class="emty_f" width="16" height="16" viewBox="0 0 18 22" xmlns="http://www.w3.org/2000/svg">
        <path d="M10.6234 0C10.7094 0.0286963 10.7955 0.0573925 10.8816 0.0803496C11.7655 0.338616 12.3624 1.11342 12.3796 2.0317C12.3853 2.26127 12.3796 2.49084 12.3796 2.7491C12.4657 2.7491 12.5403 2.7491 12.6149 2.7491C13.7685 2.7491 14.9221 2.7491 16.0699 2.7491C17.1432 2.7491 17.872 3.48373 17.872 4.55697C17.872 5.29159 17.872 6.03196 17.872 6.76658C17.872 7.28885 17.608 7.55286 17.0743 7.5586C16.9882 7.5586 16.9078 7.5586 16.8103 7.5586C16.7758 8.24731 16.7471 8.90732 16.7127 9.57308C16.6037 11.863 16.4946 14.1473 16.3856 16.4372C16.3282 17.6654 16.2823 18.8994 16.2019 20.1276C16.133 21.1664 15.2607 21.9699 14.2219 21.9928C13.7398 22.0043 13.2634 21.9986 12.7813 21.9986C9.79117 21.9986 6.79528 21.9986 3.80513 21.9986C2.72041 21.9986 1.85952 21.3213 1.69308 20.2997C1.62421 19.8578 1.62421 19.3987 1.60125 18.951C1.49221 16.7012 1.3889 14.4457 1.28559 12.1959C1.21672 10.6865 1.14211 9.17707 1.0675 7.66764C1.0675 7.63895 1.05602 7.61599 1.05028 7.56434C0.92402 7.56434 0.809235 7.57008 0.688711 7.56434C0.298441 7.55286 0.00573925 7.27164 0 6.88137C0.00573925 6.04917 0 5.21698 0.0172178 4.39053C0.0344355 3.48947 0.792017 2.76058 1.7103 2.75484C2.89258 2.74336 4.07487 2.75484 5.25142 2.75484C5.32603 2.75484 5.40638 2.75484 5.50968 2.75484C5.50968 2.54249 5.50968 2.34162 5.50968 2.14648C5.51542 1.06176 6.14674 0.275484 7.19703 0.0344355C7.22572 0.0286963 7.24868 0.0114785 7.27164 0C8.39079 0 9.50421 0 10.6234 0ZM2.47362 7.57008C2.47362 7.77095 2.46788 7.94887 2.47362 8.12679C2.58266 10.4225 2.69171 12.7182 2.80076 15.0082C2.87537 16.6151 2.94998 18.2279 3.03033 19.8349C3.05902 20.3858 3.31155 20.6154 3.86252 20.6154C7.24868 20.6154 10.6348 20.6154 14.021 20.6154C14.5834 20.6154 14.8302 20.3858 14.8589 19.8176C14.9508 17.8893 15.0426 15.9551 15.1344 14.0267C15.2148 12.3624 15.2951 10.698 15.3755 9.02785C15.3984 8.54575 15.4214 8.05791 15.4386 7.56434C11.1055 7.57008 6.79528 7.57008 2.47362 7.57008ZM16.5061 6.18118C16.5061 5.65891 16.5061 5.15959 16.5061 4.66027C16.5061 4.21835 16.42 4.13226 15.9838 4.13226C11.2891 4.13226 6.5944 4.13226 1.89969 4.13226C1.85952 4.13226 1.8136 4.13226 1.77343 4.13226C1.5209 4.138 1.3889 4.26427 1.3889 4.51679C1.38316 5.02759 1.3889 5.53264 1.3889 6.04344C1.3889 6.08935 1.40038 6.13526 1.40612 6.18692C6.43944 6.18118 11.4613 6.18118 16.5061 6.18118ZM11.0079 2.74336C11.0079 2.4851 11.0194 2.24979 11.0079 2.01448C10.9907 1.66438 10.7037 1.38316 10.3479 1.37742C9.41812 1.37168 8.48836 1.37168 7.5586 1.37742C7.23146 1.37742 6.93876 1.62995 6.90432 1.95135C6.87563 2.20961 6.89858 2.47362 6.89858 2.74336C8.27027 2.74336 9.62473 2.74336 11.0079 2.74336Z" fill="var(--font-color1)"/>
        <path d="M6.20236 14.1013C6.20236 15.5763 6.20236 17.0513 6.20236 18.5263C6.20236 19.0371 5.73748 19.3757 5.28408 19.2035C5.01433 19.1002 4.83642 18.8592 4.83068 18.5607C4.82494 18.2451 4.83068 17.9294 4.83068 17.6138C4.83068 14.9737 4.83068 12.3279 4.83068 9.68784C4.83068 9.14262 5.28982 8.79826 5.76043 8.98192C6.0474 9.09096 6.20236 9.34349 6.20236 9.69932C6.20236 11.1628 6.20236 12.6321 6.20236 14.1013Z" fill="var(--font-color1)"/>
        <path d="M9.63986 14.0841C9.63986 15.5591 9.63986 17.0341 9.63986 18.5091C9.63986 19.0371 9.17498 19.3815 8.71584 19.2036C8.44035 19.1003 8.26818 18.8535 8.26818 18.5378C8.26244 17.9811 8.26818 17.4187 8.26818 16.8619C8.26818 14.4687 8.26818 12.0811 8.26818 9.68788C8.26818 9.14839 8.72732 8.80403 9.20367 8.98769C9.47916 9.09674 9.63986 9.34352 9.63986 9.68214C9.63986 11.0136 9.63986 12.3452 9.63986 13.6767C9.63986 13.8087 9.63986 13.9464 9.63986 14.0841Z" fill="var(--font-color1)"/>
        <path d="M13.0674 14.1128C13.0674 15.5821 13.0674 17.0456 13.0674 18.5148C13.0674 18.8247 12.9354 19.0543 12.6542 19.1863C12.3844 19.3068 12.1376 19.2552 11.9195 19.06C11.7474 18.9051 11.69 18.7099 11.6957 18.4804C11.7014 17.2866 11.6957 16.0871 11.6957 14.8933C11.6957 13.1543 11.6957 11.4154 11.6957 9.67636C11.6957 9.13687 12.1721 8.79825 12.6369 8.98765C12.9124 9.09669 13.0674 9.34922 13.0674 9.69358C13.0731 11.1628 13.0674 12.6378 13.0674 14.1128Z" fill="var(--font-color1)"/>
      </svg>
    </div>
    <div *ngSwitchCase="'поделиться'">
      <svg width="16" height="16" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M15.9238 7.01009C13.5066 8.21941 11.0965 9.42157 8.66504 10.638C8.9368 11.5325 8.94395 12.427 8.66504 13.3357C11.0822 14.5451 13.4923 15.7544 15.8737 16.9422C16.2456 16.5988 16.5745 16.2267 16.9607 15.9476C19.6353 13.9941 23.4542 15.6113 23.9334 18.8957C24.3481 21.7509 22.0168 24.2268 19.1347 23.9835C17.0894 23.8117 15.3731 22.1516 15.1514 20.1051C15.1085 19.6829 15.1156 19.2464 15.1729 18.8313C15.2086 18.588 15.1442 18.5093 14.9512 18.4163C12.8772 17.3859 10.8105 16.3483 8.73656 15.3107C8.50056 15.1891 8.25741 15.0817 8.02141 14.9458C7.87838 14.8671 7.79972 14.8885 7.6853 15.0102C6.76276 15.9834 5.63283 16.4556 4.28836 16.4413C2.21445 16.4127 0.326467 14.7454 0.0475605 12.6846C-0.288557 10.2588 1.18464 8.12638 3.58752 7.63979C5.16084 7.32494 6.52676 7.79722 7.65669 8.94213C7.80687 9.09241 7.89984 9.10672 8.07862 9.01369C10.3742 7.85447 12.677 6.70955 14.9798 5.56464C15.1585 5.47877 15.2158 5.40005 15.1729 5.18538C14.7295 2.75959 16.4458 0.39105 18.8773 0.047575C21.4232 -0.317367 23.7117 1.45725 23.9763 3.99753C24.2266 6.37323 22.4673 8.57004 20.093 8.8348C18.4196 9.028 17.0751 8.41977 16.0239 7.12458C15.9881 7.09596 15.9595 7.06018 15.9238 7.01009ZM7.14179 11.9976C7.14179 10.5021 5.91174 9.26414 4.42424 9.2713C2.94389 9.27845 1.721 10.4949 1.70669 11.9762C1.69239 13.4574 2.92244 14.7025 4.41709 14.7168C5.90459 14.7311 7.14179 13.5003 7.14179 11.9976ZM16.832 19.5541C16.8248 21.0496 18.0549 22.2876 19.5424 22.2804C21.0227 22.2804 22.2671 21.0353 22.2671 19.5541C22.2599 18.0728 21.0513 16.8635 19.5567 16.8492C18.062 16.8277 16.8391 18.0442 16.832 19.5541ZM19.5424 7.14605C21.037 7.14605 22.2671 5.91527 22.2671 4.42688C22.2599 2.93849 21.0156 1.70055 19.5352 1.7077C18.0477 1.71486 16.8463 2.93133 16.832 4.41972C16.8248 5.92242 18.0406 7.14605 19.5424 7.14605Z"
          fill="var(--font-color1)" />
      </svg>
    </div>
    <div *ngSwitchCase="'мне нравится'">
      <div class="flex items-center icons_w lik_hov"
           [ngClass]="{'is-liked': false}">
        <div class="icon-wrap like_w">
          <svg width="16" height="15" viewBox="0 0 24 22"
               xmlns="http://www.w3.org/2000/svg">
            <path
              d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
              fill="var(--font-color1)" />
          </svg>
        </div>

      </div>
    </div>

  </ng-container>
</ng-template>
