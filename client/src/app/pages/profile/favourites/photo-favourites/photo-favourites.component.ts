import {Component, ElementRef, inject, ViewChild} from "@angular/core";
import {CommonModule, NgOptimizedImage} from "@angular/common";
import {environment} from "@/env/environment";
import {ToasterService} from "@/services/toaster.service";
import {PhotoService} from "@/services/photo.service";
import {Router} from "@angular/router";
import {AuthService} from "@/services/auth.service";
import {ShareDataService} from "@/services/share-data.service";
import { ActionsSettings, PhotoGalleryComponent } from "@/components/photo-gallery/photo-gallery.component";

@Component({
  selector: 'PhotoFavourites',
  standalone: true,
  imports: [CommonModule, NgOptimizedImage, PhotoGalleryComponent],
  templateUrl: './photo-favourites.component.html',
  styleUrl: '../favourites.component.scss'
})
export class PhotoFavouritesComponent {
  @ViewChild('confirmDialog') confirmDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  toasterService = inject(ToasterService);
  photoService = inject(PhotoService);
  router = inject(Router);
  auth = inject(AuthService)
  shareDataService = inject(ShareDataService);
  message: string = "";

  items: any = []
  page: number = 1;
  totalPages = 1;
  actionsSettings: ActionsSettings = this.getActionsSettings();

  ngOnInit() {
    this.get()
  }

  get() {
    this.photoService
      .getFavourites(true, this.page)
      .subscribe((res: any) => {
        this.totalPages = res.pagination.totalPages;
        this.items = [...this.items, ...res.items]
      })
  }

  getUrl(item: any) {
    if(item && item.name) {
      const pathMatch = item.name.match(/^(photo\/[^\/]+)/);
      let url = '';

      if (pathMatch && pathMatch[1]) {
        const photoPath = pathMatch[1];
        url = this.environment.baseUrl + '/ru/' + photoPath;
      } else {
        url = this.environment.baseUrl + '/ru/photo/' + item.slug;
      }

      // Add photo parameter to the URL for direct photo access
      if (item.id) {
        url += '?photo=' + item.id;
      }

      return url;
    }
    return ''
  }

    private getActionsSettings(): ActionsSettings {
    return {
      viewIcon: {
        action: (item: any) => this.redirect(item)
      },
      likeIcon: {
        action: (item: any) => this.like(item)
      },
      shareIcon: {
        action: (item: any) => this.copyUrl(item)
      },
      deleteIcon: {
        action: (item: any) => this.favorite(item.id)
      },
    };
  }

  copyUrl(item: any)  {
    if (item && item.name) {
      const url = this.getUrl(item);

      // Try modern clipboard API first
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(url)
          .then(() => {
            this.toasterService.showToast('Ссылка скопирована в буфер обмена!', 'success', 'bottom-middle', 3000);
          })
          .catch(err => {
            console.error('Clipboard API failed, trying fallback: ', err);
            this.fallbackShare(url);
          });
      } else {
        // Fallback for browsers that don't support clipboard API
        this.fallbackShare(url);
      }
    }
  }

  private fallbackShare(url: string) {
    // Try Web Share API first (works well on mobile)
    if (navigator.share) {
      navigator.share({
        title: 'Фото',
        url: url
      }).then(() => {
        this.toasterService.showToast('Ссылка поделена!', 'success', 'bottom-middle', 3000);
      }).catch(err => {
        console.error('Web Share API failed, trying legacy fallback: ', err);
        this.legacyFallbackCopy(url);
      });
    } else {
      // Legacy fallback using execCommand
      this.legacyFallbackCopy(url);
    }
  }

  private legacyFallbackCopy(url: string) {
    try {
      const textArea = document.createElement('textarea');
      textArea.value = url;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';

      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      const successful = document.execCommand('copy');
      document.body.removeChild(textArea);

      if (successful) {
        this.toasterService.showToast('Ссылка скопирована в буфер обмена!', 'success', 'bottom-middle', 3000);
      } else {
        this.showManualCopyOption(url);
      }
    } catch (error) {
      console.error('Legacy copy failed:', error);
      this.showManualCopyOption(url);
    }
  }

  private showManualCopyOption(url: string) {
    // As a last resort, show the URL to the user for manual copying
    this.toasterService.showToast('Скопируйте ссылку: ' + url, 'info', 'bottom-middle', 8000);
  }

  favorite(id: number) {
    this.openConfirmationDialog('Удалить фото из избранного?').then((confirmed) => {
      if (confirmed) {
        this.photoService.addToFavourites(id).subscribe({
          next: () => {
            this.toasterService.showToast('Фото удалено из избранного!', 'success', 'bottom-middle', 3000);
            const photoIndex = this.items.findIndex((e: any) => e.id === id);
            this.items.splice(photoIndex, 1);
          },
          error: () => {
            this.openModal('Ошибка удаления, попробуйте еще раз')
          }
        });
      }
    });
  }

  redirect(item: any) {
    const url = this.getUrl(item);
    if (url) {
      // Remove the photo parameter to go to the album instead of specific photo
      const urlWithoutPhoto = url.split('?')[0];
      this.router.navigateByUrl(urlWithoutPhoto.replace(this.environment.baseUrl, ''));
    }
  }

  openConfirmationDialog(message: string): Promise<boolean> {
    this.message = message;
    this.confirmDialog.nativeElement.showModal();
    return new Promise((resolve) => {
      const okButton = this.confirmDialog.nativeElement.querySelector('.ok-button');
      const cancelButton = this.confirmDialog.nativeElement.querySelector('.cancel-button');
      const closeDialog = (confirmed: boolean) => {
        this.closeModal(this.confirmDialog.nativeElement);
        resolve(confirmed);
      };
      okButton?.addEventListener('click', () => closeDialog(true), { once: true });
      cancelButton?.addEventListener('click', () => closeDialog(false), { once: true });
    });
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  openModal(message: string) {
    this.message = message;
    this.modal.nativeElement.showModal();
  }

  like(item: any) {
    if (!this.auth.token()) {
      this.shareDataService.showInfoModal('error');
      return;
    }

    this.photoService.like(item.id).subscribe(() => {
      item.liked = !item.liked;
      if(item.liked) item.likes++;
      else item.likes--;
    })
  }

  protected readonly environment = environment;
}
