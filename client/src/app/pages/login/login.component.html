<dialog class="stylized_wide" #modal>
  <div (click)="closeModal(modal)" class="x_bt"></div>
  <div class="flex flex-col cont_mod">
    <p class="pr_20 text-center">Заполните данные</p>
    <div class="format-options"></div>
    <p class="auth_head mt-4">
      {{ modalMessage }}
    </p>
    <div class="filter-buttons-container flex justify-center mt-4">
      <button class="save-btn" (click)="closeModal(modal)">
        <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="218" height="54" alt="bg">
        <div class="save-btn-label">Закрыть</div>
      </button>
    </div>
  </div>
</dialog>
<div class="login-page flex flex-col gap-[30px] justify-center items-center">
  <div class="login-title">Намасте!</div>
  <img src="assets/images/login-icon.svg" alt="login-icon" width="336" height="38">
  <form class="flex flex-col gap-[20px]" [formGroup]="form" (ngSubmit)="signIn()">
    <div class="form-control">
      <label for="email">Логин</label>
      <input id="email" type="text" formControlName="email">
    </div>
    <div class="form-control">
      <label for="password">Пароль</label>
      <div class="input-wrap">
        <input id="password" [type]="showPasswordState ? 'text' : 'password'" formControlName="password">
        <span class="password-suffix" (click)="showPassword()"></span>
      </div>
    </div>
    <button class="button_sign_in" type="submit">Войти</button>
    <button class="button_sign_in_google flex items-center justify-center gap-[10px]"  (click)="signInGoogle()">
      <img src="../../../assets/images/icons/google-icon.svg" width="26" height="26" alt="google">
      <span>Войти с помощью Google</span>
    </button>
    <a (click)="toRegisterPage()">нужно Зарегестрироваться</a>
    <a [routerLink]="['/ru/forgot']">Забыли пароль?</a>
  </form>
</div>
