<dialog class="stylized_wide" #modal>
  <div (click)="closeModal(modal)" class="x_bt"></div>
  <div class="flex flex-col cont_mod">
    <p class="pr_20 text-center">Фильтры</p>
    <div class="format-options"></div>
    <!-- <p class="auth_head">
      Выбор категории
    </p>
    <div class="catg_wrap">
      <app-custom-dropdown placeholderText="Выбор категории" [type]="'select'" [options]="categories" [title]="'title'" [selected]="[selectedCategory]"
        (selectedChange)="navigateToCategory($event)">
      </app-custom-dropdown>
    </div> -->
    <p class="auth_head mt-4">
      Выбор тегов
    </p>
    @if(tags) {
    <app-custom-dropdown placeholderText="Выбор тегов" [type]="'multiselect'" [options]="tags" [selected]="selectedTags"
      (selectedChange)="toggleTagSelection($event)" class="a">
    </app-custom-dropdown>
    }
    <div class="filter-buttons-container flex justify-between mt-4">
      <button class="save-btn" (click)="resetAndCloseModal(modal)">
        <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="218" height="54" alt="bg">
        <div class="save-btn-label">Сбросить все</div>
      </button>
      <button class="save-btn" (click)="closeModal(modal)">
        <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="218" height="54" alt="bg">
        <div class="save-btn-label">Показать</div>
      </button>
    </div>
  </div>
</dialog>
<div>
  <div class="middle_stripe">
    <breadcrumb></breadcrumb>
    <div class="wrapper_line">
      <div class="dec_head _background">
        <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
        <h1 class="dec_head-title_">{{categoryName}}</h1>
        <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      </div>
      <div class="cat_wrap">
        <form [formGroup]="filter">
          <div class="articles-search relative">
            <input formControlName="search" type="text" placeholder="Поиск" (input)="applyFilter()">
            <div (click)="openModal()" class="p_filter">
              <span>Фильтр</span>
            </div>
            <div class="articles-sort_ custom-dropdown" (click)="toggleDropdown()">
              @if (dropdownOpen) {
              <div class="dropdown-content">
                @for(option of sortOptions; track option.id) {
                  <div
                    (click)="selectSort(option.value)"
                    class="dropdown-item cat_i"
                    [class.active]="currentSortField === option.value">
                    {{ option.label }}
                    <span class="sort-arrow" *ngIf="currentSortField === option.value">
                      <svg [ngClass]="{'rotate-down': sortDirection === 'Desc', 'rotate-up': sortDirection === 'Asc'}" width="30" height="30" viewBox="0 0 24 24"><path d="M7 10l5 5 5-5H7z"/></svg>
                    </span>
                  </div>
                }
              </div>
              }
            </div>
          </div>
          <div class="flex articles-tag">
            <div class="tags_wrap">
              @for(tag of selectedTags; track tag.id) {
              <p class="tag_item_wrapper">
                <span class="tag_item_">{{tag.name}}
                  <span class="x_a" (click)="removeTag(tag)"></span>
                </span>
              </p>
              }
            </div>
            <div class="articles-sort custom-dropdown" (click)="toggleDropdown()">
              <button class="dropdown-btn"></button>
              @if (dropdownOpen) {
              <div class="dropdown-content">
                @for(option of sortOptions; track option.id) {
                <div (click)="selectSort(option.value)" class="dropdown-item"
                  [class.active]="filter.get('sort')?.value === option.value">
                  {{ option.label }}
                </div>
                }
              </div>
              }
            </div>
          </div>
          <!-- <div class="articles-tag">
            <select formControlName="tag" (change)="applyFilter()">
              <option value="">Все теги</option>
              @for(tag of tags; track tag.id) {
              <option [value]="tag.id">{{tag.name}}</option>
              }
            </select>
          </div> -->
          <!-- <div class="articles-category">
            <select formControlName="category" (change)="applyFilter()">
              <option value="">Все категории</option>
              @for(category of categories; track category.id) {
              <option [value]="category.id">{{category.title}}</option>
              }
            </select>
          </div> -->
        </form>
        <div>
          @for(content of contentService.list(); track content.id; let i = $index; let last = $last) {
          <div [ngClass]="{'widen': isOpened[i], 'last': last}" class="article-item m">
            <div class="vis_part relative">
              <div (click)="isOpened[i] = !isOpened[i]" class="art_img_"></div>
              <div (click)="isOpened[i] = !isOpened[i]" class="art_img">
                @if(content.preview) {
                <img style="object-fit: cover" width="66" height="66"
                  [ngSrc]="environment.serverUrl + '/upload/' + content.preview.name">
                } @else {
                <img src="assets/images/clouds.webp" alt="image">
                }
              </div>
              <div class="flex justify-between wwrap_">
                <div class="titl_w wwrap__">
                  <div class="article-title ov_wrap wwrap" (click)="$event.stopPropagation();navigate(content)">{{content.title}}
                  </div>
                  <div class="article-category wwrap">{{ content.author || content.category.title}}</div>
                </div>
                <div class="actions_w" (click)="showMenu(i, $event)"
                    [class.show_menu]="show_menu[i]">
                  <div class="flex items-center cursor-pointer icons_w show_md">
                    <div class="icon-wrap star_w no_h">
                      <svg width="7" height="22" viewBox="0 0 7 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="3.27778" cy="3.02778" r="2.27778" stroke="var(--text-color)" />
                        <circle cx="3.27778" cy="10.9999" r="2.27778" stroke="var(--text-color)" />
                        <circle cx="3.27778" cy="18.9721" r="2.27778" stroke="var(--text-color)" />
                      </svg>
                    </div>
                  </div>
                  <div class="md_chg">
                    <div class="flex items-center cursor-pointer icons_w fav_hov"
                      [ngClass]="{'in-favourites': content.inFavourites}" (click)="favorites(content);$event.stopPropagation();">
                      <div class="icon-wrap star_w favr_">
                        <svg class="emty_f" width="25" height="24" viewBox="0 0 25 24" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                            fill="var(--font-color1)" />
                        </svg>
                        <svg class="emty_f_hover" width="25" height="24" viewBox="0 0 25 24" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M5.96868 23.9618C5.94253 23.9618 5.91638 23.9618 5.89022 23.9618C5.36715 23.9356 4.89638 23.6999 4.53023 23.3333C4.16408 22.9143 3.98101 22.3906 4.03332 21.8145C4.05947 21.5789 4.11178 21.317 4.19024 21.0552C4.71331 19.4055 5.41946 17.2844 6.1256 15.1372C4.42562 13.9065 2.72564 12.6758 1.02565 11.4189C0.816424 11.2618 0.162584 10.7904 0.0318159 9.95249C-0.0727985 9.35022 0.0841231 8.77414 0.450273 8.35517C0.842577 7.88383 1.44411 7.62197 2.15026 7.62197C2.90871 7.62197 3.66717 7.62197 4.42562 7.62197H8.55789C8.68866 7.25537 8.79327 6.88878 8.92404 6.52218C9.08096 6.05084 9.21173 5.60568 9.36865 5.13435C9.7348 4.00837 10.0748 2.8562 10.4671 1.73022C10.5979 1.31125 10.8333 0.918472 11.1209 0.604245C11.4871 0.211462 12.0363 -0.0242083 12.5855 0.00197721C13.1348 0.0281628 13.6578 0.290018 14.024 0.735173C14.2332 1.02321 14.4163 1.33744 14.5471 1.70404C15.044 3.24899 15.5409 4.79393 16.0378 6.33888L16.4563 7.62197H17.6593C19.3332 7.62197 21.007 7.62197 22.6808 7.62197C23.047 7.62197 23.387 7.67434 23.7008 7.77909C24.2762 7.98857 24.7208 8.43372 24.9039 9.00981C25.087 9.58589 25.0085 10.1882 24.6685 10.7119C24.4854 10.9999 24.25 11.2356 23.9624 11.4451C22.2624 12.6758 20.5624 13.9327 18.8886 15.1372C19.5424 17.1273 20.1962 19.0912 20.8239 21.0813C21.0593 21.7884 21.0332 22.3906 20.7455 22.9405C20.327 23.7785 19.3593 24.1974 18.4701 23.9094C18.1301 23.8046 17.8163 23.6475 17.5286 23.438C16.1424 22.443 14.7563 21.4218 13.344 20.4005L12.5071 19.7983L11.5132 20.5314C10.1794 21.5003 8.84558 22.4954 7.51174 23.4642C6.96252 23.7785 6.4656 23.9618 5.96868 23.9618ZM3.45794 9.14073C3.01333 9.14073 2.59487 9.14073 2.15026 9.14073C1.91487 9.14073 1.7318 9.21929 1.65334 9.32403C1.57488 9.40259 1.54872 9.53352 1.57488 9.66445C1.60103 9.76919 1.65334 9.90012 1.94103 10.1096C3.71947 11.3927 5.47176 12.702 7.25021 13.9851C7.69482 14.3255 7.85174 14.7968 7.69482 15.3205C6.96252 17.5463 6.23022 19.7983 5.68099 21.5003C5.62869 21.6312 5.60253 21.7884 5.60253 21.8931C5.60253 22.0502 5.62869 22.155 5.70715 22.2597C5.75945 22.3383 5.86407 22.3644 5.94253 22.3644C6.0733 22.3644 6.23022 22.3383 6.54406 22.1026C7.87789 21.1337 9.21173 20.1387 10.5456 19.1698L12.4809 17.7558L14.2594 19.0389C15.6455 20.0601 17.0317 21.0552 18.4178 22.0764C18.5747 22.1811 18.7316 22.2597 18.9147 22.3121C19.0716 22.3644 19.2286 22.2859 19.307 22.1288C19.3593 22.024 19.4116 21.8407 19.2809 21.4741C18.6009 19.4055 17.9209 17.3368 17.2409 15.2682C17.0578 14.7183 17.2147 14.2731 17.6593 13.9327C19.4116 12.6758 21.1901 11.3665 22.9424 10.0834C23.0731 9.97867 23.1777 9.87393 23.2824 9.743C23.3608 9.63826 23.387 9.50733 23.3347 9.40259C23.3085 9.29785 23.2039 9.21929 23.0993 9.16692C22.9685 9.11455 22.7854 9.08836 22.6024 9.08836C20.9285 9.08836 19.2547 9.08836 17.5809 9.08836H15.2794L15.0963 8.59084C15.0701 8.51228 15.044 8.45991 15.0178 8.40754L14.4948 6.78404C13.9978 5.23909 13.5009 3.69414 13.004 2.14919C12.9517 1.96589 12.8471 1.78259 12.7425 1.65167C12.664 1.54692 12.5332 1.52074 12.4809 1.52074C12.4286 1.52074 12.324 1.52074 12.2455 1.62548C12.1148 1.75641 12.0102 1.96589 11.9317 2.17538C11.5656 3.30136 11.1994 4.45352 10.8333 5.55331C10.6763 6.02465 10.5456 6.46981 10.3886 6.94115C10.2579 7.30775 10.1533 7.70053 10.0225 8.06713L9.68249 9.14073H4.42562C4.11178 9.14073 3.77178 9.14073 3.45794 9.14073Z"
                            fill="var(--text-color)" />
                        </svg>
                      </div>
                      <p class="show_p_md cdf">
                        добавить в избранное
                      </p>
                      <div class="on_hov">
                        добавить в избранное
                      </div>
                    </div>
                    <div class="flex items-center cursor-pointer icons_w shr_hov" (click)="share(content);$event.stopPropagation();">
                      <div class="icon-wrap share_w">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M15.9238 7.01009C13.5066 8.21941 11.0965 9.42157 8.66504 10.638C8.9368 11.5325 8.94395 12.427 8.66504 13.3357C11.0822 14.5451 13.4923 15.7544 15.8737 16.9422C16.2456 16.5988 16.5745 16.2267 16.9607 15.9476C19.6353 13.9941 23.4542 15.6113 23.9334 18.8957C24.3481 21.7509 22.0168 24.2268 19.1347 23.9835C17.0894 23.8117 15.3731 22.1516 15.1514 20.1051C15.1085 19.6829 15.1156 19.2464 15.1729 18.8313C15.2086 18.588 15.1442 18.5093 14.9512 18.4163C12.8772 17.3859 10.8105 16.3483 8.73656 15.3107C8.50056 15.1891 8.25741 15.0817 8.02141 14.9458C7.87838 14.8671 7.79972 14.8885 7.6853 15.0102C6.76276 15.9834 5.63283 16.4556 4.28836 16.4413C2.21445 16.4127 0.326467 14.7454 0.0475605 12.6846C-0.288557 10.2588 1.18464 8.12638 3.58752 7.63979C5.16084 7.32494 6.52676 7.79722 7.65669 8.94213C7.80687 9.09241 7.89984 9.10672 8.07862 9.01369C10.3742 7.85447 12.677 6.70955 14.9798 5.56464C15.1585 5.47877 15.2158 5.40005 15.1729 5.18538C14.7295 2.75959 16.4458 0.39105 18.8773 0.047575C21.4232 -0.317367 23.7117 1.45725 23.9763 3.99753C24.2266 6.37323 22.4673 8.57004 20.093 8.8348C18.4196 9.028 17.0751 8.41977 16.0239 7.12458C15.9881 7.09596 15.9595 7.06018 15.9238 7.01009ZM7.14179 11.9976C7.14179 10.5021 5.91174 9.26414 4.42424 9.2713C2.94389 9.27845 1.721 10.4949 1.70669 11.9762C1.69239 13.4574 2.92244 14.7025 4.41709 14.7168C5.90459 14.7311 7.14179 13.5003 7.14179 11.9976ZM16.832 19.5541C16.8248 21.0496 18.0549 22.2876 19.5424 22.2804C21.0227 22.2804 22.2671 21.0353 22.2671 19.5541C22.2599 18.0728 21.0513 16.8635 19.5567 16.8492C18.062 16.8277 16.8391 18.0442 16.832 19.5541ZM19.5424 7.14605C21.037 7.14605 22.2671 5.91527 22.2671 4.42688C22.2599 2.93849 21.0156 1.70055 19.5352 1.7077C18.0477 1.71486 16.8463 2.93133 16.832 4.41972C16.8248 5.92242 18.0406 7.14605 19.5424 7.14605Z"
                            fill="var(--font-color1)" />
                        </svg>
                      </div>
                      <p class="show_p_md">
                        поделиться
                      </p>
                      <div class="on_hov">
                        поделиться
                      </div>
                    </div>
                    <button class="flex items-center icons_w lik_hov" [ngClass]="{'is-liked': content.liked}"
                      (click)="like(content);$event.stopPropagation();">
                      <div class="icon-wrap like_w">
                        <svg class="emty_l" width="24" height="22" viewBox="0 0 24 22" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                            fill="var(--font-color1)" />
                        </svg>
                        <svg class="emty_l_hover" width="24" height="22" viewBox="0 0 24 22" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M11.7534 2.35249C11.576 2.19912 11.4123 2.05272 11.2486 1.89935C10.048 0.749078 8.90875 0.163484 7.67404 0.0589139C7.44892 0.0379998 7.22381 0.0310285 7.00552 0.0310285C4.87718 0.0310285 3.11038 0.909419 1.72559 2.65923C0.286236 4.47875 -0.252671 6.65381 0.108874 9.12864C0.402203 11.1294 1.2549 12.7816 2.63969 14.0365C4.74757 15.9397 6.91684 17.8428 9.01789 19.6763C9.79555 20.3595 10.5732 21.0357 11.3509 21.7189C11.4668 21.8235 11.5896 21.9002 11.7056 21.942C12.0399 22.0675 12.3468 21.9908 12.6674 21.698C13.8612 20.6453 15.055 19.5926 16.2556 18.54C17.0742 17.815 17.8996 17.0969 18.7182 16.3719C19.0047 16.1209 19.2981 15.8769 19.5777 15.6329C20.2258 15.0822 20.8943 14.5175 21.4946 13.8971C23.6229 11.6802 24.4006 9.03105 23.8071 6.03336C23.3023 3.49579 21.9517 1.69718 19.796 0.679364C17.7291 -0.296625 15.7713 -0.21994 13.9908 0.902448C13.3223 1.32073 12.7016 1.87844 12.1422 2.55466L12.074 2.63831L11.9989 2.5686C11.9171 2.49192 11.8352 2.4222 11.7534 2.35249ZM12.7561 4.47875C12.8175 4.38812 12.8789 4.2975 12.9403 4.20687C13.1586 3.87922 13.3837 3.54459 13.6566 3.25877C15.13 1.69021 17.4426 1.39044 19.4072 2.52677C20.8602 3.36334 21.7538 4.69486 22.0744 6.47953C22.5315 9.0659 21.7675 11.2689 19.7892 13.0187C18.0224 14.5802 16.2215 16.1697 14.4752 17.6964C13.7316 18.3517 12.9881 19.0001 12.2513 19.6554C12.2172 19.6902 12.1763 19.7181 12.1354 19.753L11.9989 19.8645L8.01512 16.3579C7.59218 15.9815 7.15559 15.605 6.73265 15.2425C5.79809 14.4338 4.82943 13.5973 3.90851 12.7328C2.5783 11.485 1.8211 9.77698 1.78699 7.92957C1.75288 6.17976 2.37365 4.51361 3.49921 3.36334C5.02043 1.80872 7.43528 1.39741 9.23618 2.38037C9.94563 2.77077 10.5459 3.35636 11.0576 4.17201C11.0848 4.21384 11.1121 4.25567 11.1326 4.2975C11.1667 4.36024 11.2008 4.41601 11.2417 4.47178C11.4191 4.73669 11.692 4.89703 11.9853 4.89703H11.9921C12.2923 4.904 12.5719 4.75063 12.7561 4.47875Z"
                            fill="var(--text-color)" />
                        </svg>
                      </div>
                      <p class="show_p_md">
                        мне нравится
                      </p>
                      <span class="ml-2 text-color default_">
                        {{content.likes}}
                      </span>

                      <div class="on_hov">
                        мне нравится
                      </div>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div class="invis_part">
              <div class="article-content" [innerHTML]="content.content"></div>
              <p (click)="$event.stopPropagation();navigate(content)" class="link_more">
                Читать далее
              </p>
              <div class="tags_cont">
                @for (tag of content.tags; track $index) {
                <a (click)="$event.preventDefault(); addTagFilter(tag)">
                  <div class="tag_item">{{tag.name}}</div>
                </a>
                }
              </div>
              <div class="flex">
                <div class="flex items-center icons_w b_p">
                  <div class="icon-wrap clock_w">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M11.25 0C11.75 0 12.25 0 12.75 0C12.8125 0.0125261 12.8687 0.0313152 12.9312 0.0375783C13.6812 0.169102 14.4625 0.212944 15.1812 0.438413C19.9188 1.8977 22.8062 5.06681 23.8125 9.93319C23.9 10.3653 23.9375 10.81 24 11.2484C24 11.7495 24 12.2505 24 12.7516C23.9875 12.8142 23.9688 12.8706 23.9625 12.9332C23.8375 14.5804 23.4187 16.1524 22.6125 17.5992C20.3438 21.6451 16.8875 23.8121 12.25 23.9937C9.75625 24.0939 7.44375 23.3987 5.36875 22.0021C2.54375 20.0856 0.8125 17.4489 0.1875 14.0919C0.10625 13.6472 0.0625 13.1962 0 12.7516C0 12.2505 0 11.7495 0 11.2484C0.0125 11.1795 0.03125 11.1106 0.0375 11.0418C0.11875 10.4969 0.16875 9.93946 0.28125 9.40084C1.25 4.74739 5.21875 0.951983 9.90625 0.187891C10.3562 0.112735 10.8 0.0626305 11.25 0ZM1.925 12.9395C2.375 18.0125 6.55 21.7328 11.0625 22.0647C11.0625 21.8017 11.0625 21.5386 11.0625 21.2693C11.0688 20.6931 11.4563 20.286 12 20.286C12.5437 20.286 12.9312 20.6931 12.9375 21.2693C12.9375 21.5324 12.9375 21.7954 12.9375 22.0647C17.7875 21.714 21.75 17.5741 22.0562 12.9395C21.9375 12.9395 21.825 12.9395 21.7062 12.9395C20.7125 12.9395 20.2812 12.6514 20.2812 11.9937C20.2875 11.3486 20.7125 11.0605 21.6938 11.0605C21.8188 11.0605 21.9375 11.0605 22.0625 11.0605C21.65 5.92484 17.2875 2.19207 12.9375 1.94781C12.9375 2.20459 12.9375 2.46138 12.9375 2.71816C12.9312 3.30689 12.5437 3.72025 11.9875 3.71399C11.45 3.70772 11.0625 3.30063 11.0625 2.71816C11.0625 2.45511 11.0625 2.19207 11.0625 1.92902C5.75 2.39248 2.15625 6.90188 1.95 11.0668C2.19375 11.0668 2.43125 11.0668 2.675 11.0668C3.29375 11.0668 3.70625 11.4426 3.7125 12C3.7125 12.5637 3.3 12.9395 2.6625 12.9457C2.425 12.9395 2.18125 12.9395 1.925 12.9395Z"
                        fill="var(--text-color)" />
                      <path
                        d="M11.0627 9.23796C11.0627 8.34234 11.0627 7.44047 11.0627 6.54485C11.0627 5.95612 11.4565 5.5365 12.0002 5.5365C12.544 5.5365 12.9377 5.95612 12.9377 6.55111C12.9377 8.15445 12.944 9.75153 12.9315 11.3549C12.9315 11.5428 12.9815 11.6743 13.1127 11.8058C14.1752 12.858 15.2252 13.9165 16.2815 14.9686C16.5065 15.1941 16.6502 15.4572 16.619 15.7828C16.5815 16.1649 16.3815 16.4405 16.019 16.5783C15.6377 16.7223 15.294 16.6346 15.0065 16.3465C14.3377 15.6889 13.6815 15.025 13.019 14.3611C12.494 13.835 11.9752 13.3089 11.444 12.7891C11.1815 12.5323 11.0565 12.2442 11.0627 11.8747C11.069 10.9854 11.0627 10.1085 11.0627 9.23796Z"
                        fill="var(--text-color)" />
                    </svg>
                  </div>
                  <span class="ml-2 text-color">{{content.content | readingTime}}</span>
                </div>
                <div class="flex items-center icons_w b_p">
                  <div class="icon-wrap cal_w">
                    <svg width="22" height="24" viewBox="0 0 22 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M6.41826 5.02326C5.96059 5.02326 5.58105 4.64372 5.58105 4.18605V0.837209C5.58105 0.379535 5.96059 0 6.41826 0C6.87594 0 7.25547 0.379535 7.25547 0.837209V4.18605C7.25547 4.64372 6.87594 5.02326 6.41826 5.02326Z"
                        fill="var(--text-color)" />
                      <path
                        d="M15.3489 5.02326C14.8913 5.02326 14.5117 4.64372 14.5117 4.18605V0.837209C14.5117 0.379535 14.8913 0 15.3489 0C15.8066 0 16.1861 0.379535 16.1861 0.837209V4.18605C16.1861 4.64372 15.8066 5.02326 15.3489 5.02326Z"
                        fill="var(--text-color)" />
                      <path
                        d="M6.97663 14.7908C6.83151 14.7908 6.6864 14.7573 6.55244 14.7015C6.40733 14.6457 6.2957 14.5676 6.18407 14.4671C5.98314 14.255 5.86035 13.9759 5.86035 13.6745C5.86035 13.5294 5.89384 13.3843 5.94965 13.2503C6.00547 13.1164 6.08361 12.9936 6.18407 12.882C6.2957 12.7815 6.40733 12.7034 6.55244 12.6476C6.9543 12.4801 7.45663 12.5694 7.76919 12.882C7.97012 13.0941 8.09291 13.3843 8.09291 13.6745C8.09291 13.7415 8.08175 13.8196 8.07058 13.8978C8.05942 13.9648 8.0371 14.0317 8.00361 14.0987C7.98128 14.1657 7.94779 14.2327 7.90314 14.2996C7.86965 14.3555 7.81384 14.4113 7.76919 14.4671C7.5571 14.668 7.26686 14.7908 6.97663 14.7908Z"
                        fill="var(--text-color)" />
                      <path
                        d="M10.8839 14.7906C10.7387 14.7906 10.5936 14.7572 10.4597 14.7013C10.3146 14.6455 10.2029 14.5674 10.0913 14.4669C9.89037 14.2548 9.76758 13.9758 9.76758 13.6744C9.76758 13.5292 9.80107 13.3841 9.85688 13.2502C9.91269 13.1162 9.99083 12.9934 10.0913 12.8818C10.2029 12.7813 10.3146 12.7032 10.4597 12.6474C10.8615 12.4688 11.3639 12.5692 11.6764 12.8818C11.8773 13.0939 12.0001 13.3841 12.0001 13.6744C12.0001 13.7413 11.989 13.8195 11.9778 13.8976C11.9666 13.9646 11.9443 14.0316 11.9108 14.0986C11.8885 14.1655 11.855 14.2325 11.8104 14.2995C11.7769 14.3553 11.7211 14.4111 11.6764 14.4669C11.4643 14.6679 11.1741 14.7906 10.8839 14.7906Z"
                        fill="var(--text-color)" />
                      <path
                        d="M14.7911 14.7906C14.646 14.7906 14.5009 14.7572 14.3669 14.7013C14.2218 14.6455 14.1102 14.5674 13.9985 14.4669C13.9539 14.4111 13.9092 14.3553 13.8646 14.2995C13.8199 14.2325 13.7864 14.1655 13.7641 14.0986C13.7306 14.0316 13.7083 13.9646 13.6971 13.8976C13.686 13.8195 13.6748 13.7413 13.6748 13.6744C13.6748 13.3841 13.7976 13.0939 13.9985 12.8818C14.1102 12.7813 14.2218 12.7032 14.3669 12.6474C14.7799 12.4688 15.2711 12.5692 15.5836 12.8818C15.7846 13.0939 15.9074 13.3841 15.9074 13.6744C15.9074 13.7413 15.8962 13.8195 15.885 13.8976C15.8739 13.9646 15.8515 14.0316 15.8181 14.0986C15.7957 14.1655 15.7622 14.2325 15.7176 14.2995C15.6841 14.3553 15.6283 14.4111 15.5836 14.4669C15.3715 14.6679 15.0813 14.7906 14.7911 14.7906Z"
                        fill="var(--text-color)" />
                      <path
                        d="M6.97663 18.6976C6.83151 18.6976 6.6864 18.6642 6.55244 18.6084C6.41849 18.5526 6.2957 18.4744 6.18407 18.3739C5.98314 18.1618 5.86035 17.8716 5.86035 17.5813C5.86035 17.4362 5.89384 17.2911 5.94965 17.1572C6.00547 17.012 6.08361 16.8894 6.18407 16.7889C6.5971 16.3759 7.35617 16.3759 7.76919 16.7889C7.97012 17.001 8.09291 17.2911 8.09291 17.5813C8.09291 17.8716 7.97012 18.1618 7.76919 18.3739C7.5571 18.5748 7.26686 18.6976 6.97663 18.6976Z"
                        fill="var(--text-color)" />
                      <path
                        d="M10.8839 18.6976C10.5936 18.6976 10.3034 18.5748 10.0913 18.3739C9.89037 18.1618 9.76758 17.8716 9.76758 17.5813C9.76758 17.4362 9.80107 17.2911 9.85688 17.1572C9.91269 17.012 9.99083 16.8894 10.0913 16.7889C10.5043 16.3759 11.2634 16.3759 11.6764 16.7889C11.7769 16.8894 11.855 17.012 11.9108 17.1572C11.9666 17.2911 12.0001 17.4362 12.0001 17.5813C12.0001 17.8716 11.8773 18.1618 11.6764 18.3739C11.4643 18.5748 11.1741 18.6976 10.8839 18.6976Z"
                        fill="var(--text-color)" />
                      <path
                        d="M14.7911 18.6975C14.5009 18.6975 14.2106 18.5747 13.9985 18.3738C13.8981 18.2733 13.8199 18.1506 13.7641 18.0054C13.7083 17.8715 13.6748 17.7264 13.6748 17.5813C13.6748 17.4361 13.7083 17.291 13.7641 17.1571C13.8199 17.0119 13.8981 16.8892 13.9985 16.7887C14.2553 16.5319 14.646 16.4092 15.0032 16.4873C15.0813 16.4985 15.1483 16.5208 15.2153 16.5543C15.2822 16.5766 15.3492 16.6101 15.4162 16.6547C15.472 16.6882 15.5278 16.744 15.5836 16.7887C15.7846 17.0008 15.9074 17.291 15.9074 17.5813C15.9074 17.8715 15.7846 18.1617 15.5836 18.3738C15.3715 18.5747 15.0813 18.6975 14.7911 18.6975Z"
                        fill="var(--text-color)" />
                      <path
                        d="M20.3716 9.5886H1.39483C0.937152 9.5886 0.557617 9.20907 0.557617 8.75139C0.557617 8.29372 0.937152 7.91418 1.39483 7.91418H20.3716C20.8292 7.91418 21.2088 8.29372 21.2088 8.75139C21.2088 9.20907 20.8292 9.5886 20.3716 9.5886Z"
                        fill="var(--text-color)" />
                      <path
                        d="M15.3488 24H6.4186C2.34419 24 0 21.6558 0 17.5814V8.09304C0 4.01862 2.34419 1.67444 6.4186 1.67444H15.3488C19.4233 1.67444 21.7674 4.01862 21.7674 8.09304V17.5814C21.7674 21.6558 19.4233 24 15.3488 24ZM6.4186 3.34886C3.22605 3.34886 1.67442 4.90049 1.67442 8.09304V17.5814C1.67442 20.774 3.22605 22.3256 6.4186 22.3256H15.3488C18.5414 22.3256 20.093 20.774 20.093 17.5814V8.09304C20.093 4.90049 18.5414 3.34886 15.3488 3.34886H6.4186Z"
                        fill="var(--text-color)" />
                    </svg>
                  </div>
                  <span class="ml-2 text-color">{{content.created_at | date: 'dd/MM/yyyy'}}</span>
                </div>
              </div>
            </div>
          </div>
          }

          <div class="buttn_catg" *ngIf="hasMoreContent">
            <button class="load-more-button" (click)="loadMoreContent()" [disabled]="isLoading">
              <span *ngIf="!isLoading">Загрузить еще</span>
              <span *ngIf="isLoading">Загрузка...</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
