import {Component, inject} from '@angular/core';
import {BreadcrumbComponent} from "@/components/breadcrumb/breadcrumb.component";
import {CommonModule, NgForOf, NgOptimizedImage} from "@angular/common";
import {FormBuilder, ReactiveFormsModule, Validators} from "@angular/forms";
import {DonationService} from "@/services/donation.service";
import {ActivatedRoute, Router} from "@angular/router";

@Component({
  selector: 'app-donation',
  standalone: true,
  imports: [
    BreadcrumbComponent,
    NgOptimizedImage,
    CommonModule,
    ReactiveFormsModule,
  ],
  templateUrl: './donation.component.html',
  styleUrl: './donation.component.scss'
})
export class DonationComponent {
  donationService = inject(DonationService);
  fb = inject(FormBuilder)
  router = inject(Router)
  route = inject(ActivatedRoute)

  tabs: string[] = ['Банковский перевод', 'Онлайн']
  selectedTab: string = 'Банковский перевод';
  success = this.route.snapshot.queryParams['success'];

  paymentForm = this.fb.group({
    sum: [1000, Validators.required],
    type: ['stripe', Validators.required],
    autoRenew: [true]
  })

  createPayment() {
    this.donationService.createPayment(this.paymentForm.value).subscribe((res: any) => {
      location.href = res.paymentUrl;
    })
  }
}
