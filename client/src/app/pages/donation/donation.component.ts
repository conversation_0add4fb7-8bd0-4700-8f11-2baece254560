import {Component, inject} from '@angular/core';
import {BreadcrumbComponent} from "@/components/breadcrumb/breadcrumb.component";
import {CommonModule, NgOptimizedImage} from "@angular/common";
import {FormBuilder, ReactiveFormsModule, Validators} from "@angular/forms";
import {DonationService} from "@/services/donation.service";
import {ActivatedRoute, Router} from "@angular/router";
import {ToasterService} from "@/services/toaster.service";

@Component({
  selector: 'app-donation',
  standalone: true,
  imports: [
    BreadcrumbComponent,
    NgOptimizedImage,
    CommonModule,
    ReactiveFormsModule,
  ],
  templateUrl: './donation.component.html',
  styleUrl: './donation.component.scss'
})
export class DonationComponent {
  donationService = inject(DonationService);
  fb = inject(FormBuilder)
  router = inject(Router)
  route = inject(ActivatedRoute)
  toasterService = inject(ToasterService)

  tabs: string[] = ['Банковский перевод', 'Онлайн']
  selectedTab: string = 'Банковский перевод';
  success = this.route.snapshot.queryParams['success'];

  paymentForm = this.fb.group({
    sum: [100, [Validators.required, Validators.min(10)]],
    type: ['', Validators.required],
    autoRenew: [false]
  })

  constructor() {
    // Watch for payment type changes to adjust currency and validation
    this.paymentForm.get('type')?.valueChanges.subscribe(type => {
      const sumControl = this.paymentForm.get('sum');
      if (type === 'stripe') {
        // For Stripe (Europe) - minimum 10 EUR
        sumControl?.setValidators([Validators.required, Validators.min(10)]);
        if (sumControl?.value && sumControl.value < 10) {
          sumControl.setValue(10);
        }
      } else if (type === 'yookassa') {
        // For YooKassa (Russia) - minimum 100 RUB
        sumControl?.setValidators([Validators.required, Validators.min(100)]);
        if (sumControl?.value && sumControl.value < 100) {
          sumControl.setValue(100);
        }
      }
      sumControl?.updateValueAndValidity();
    });
  }

  createPayment() {
    if (this.paymentForm.valid) {
      const formValue = this.paymentForm.value;
      const currency = formValue.type === 'stripe' ? 'EUR' : 'RUB';
      const minAmount = formValue.type === 'stripe' ? 10 : 100;

      // Additional validation
      if (!formValue.type) {
        this.toasterService.showToast('Пожалуйста, выберите платежную систему', 'warning', 'bottom-middle');
        return;
      }

      if (!formValue.sum || formValue.sum < minAmount) {
        const currencySymbol = currency === 'EUR' ? '€' : '₽';
        this.toasterService.showToast(`Минимальная сумма: ${minAmount} ${currencySymbol}`, 'warning', 'bottom-middle');
        return;
      }

      this.donationService.createPayment(formValue).subscribe({
        next: (res: any) => {
          this.toasterService.showToast('Перенаправление на страницу оплаты...', 'success', 'bottom-middle', 2000);
          setTimeout(() => {
            location.href = res.paymentUrl;
          }, 1000);
        },
        error: () => {
          this.toasterService.showToast('Ошибка при создании платежа. Попробуйте еще раз.', 'error', 'bottom-middle');
        }
      })
    } else {
      // Mark all fields as touched to show validation errors
      this.paymentForm.markAllAsTouched();
      this.toasterService.showToast('Пожалуйста, заполните все обязательные поля корректно', 'warning', 'bottom-middle');
    }
  }
}
