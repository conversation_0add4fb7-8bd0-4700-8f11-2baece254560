.profile-tabs {
  display: flex;
  justify-content: center;
  z-index: 1;
}

.profile-tabs::after {
  content: '';
  background-image: var(--lib-after);
  width: 100%;
  height: 3px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  position: absolute;
  bottom: 0;
  right: 0;
  display: flex;
  z-index: 5;
}


.profile-tab.is-active {
  background: var(--tab_active);
  color: rgba(255, 255, 255, 0.9529);
  z-index: 5;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.profile-tab:not(.is-active) {
  cursor: pointer;
}

.profile-tab {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 350px;
  height: 61px;
  background: var(--tab_nominal);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  margin-right: -60px;
  font-family: Prata;
  font-weight: 400;
  font-size: 24px;
  line-height: 20px;
  color: var(--text-color);
  position: relative;
  bottom: 1px;
  cursor: pointer;
}

.payment-form input {
  width: max-content;
}

.form-control {
  padding: 7px 0px;
}

.form-control > div:first-child {
  margin-bottom: 5px;
}
