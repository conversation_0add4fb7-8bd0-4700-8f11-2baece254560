<div class="middle_stripe">
  <breadcrumb></breadcrumb>
  <div class="tab-container relative">
      <div class="dec_head _background">
        <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
        <h1 class="dec_head-title_">Пожертвование</h1>
        <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      </div>

      <div *ngIf="!success" class="profile-tabs">
        <ng-container *ngFor="let tab of tabs">
          <div class="profile-tab" [ngClass]="{'is-active': selectedTab === tab }" (click)="selectedTab = tab">
            {{tab}}
          </div>
        </ng-container>
      </div>
    </div>

  <div *ngIf="success" style="text-align: center; font-size: 24px;">
    Оплата прошла успешно
  </div>

  <div *ngIf="!success">
    <div *ngIf="selectedTab === 'Банковский перевод'">
      Здесь реквизиты
    </div>
    <div *ngIf="selectedTab === 'Онлайн'">
      <form class="payment-form" [formGroup]="paymentForm" action="">
        <div class="form-control">
          <div>Платежная система</div>
          <input formControlName="type" type="radio" value="stripe"> Stripe (Европа)
          <input formControlName="type" type="radio" value="yookassa"> ЮКасса (СНГ)
        </div>
        <div class="form-control">
          <div>Сумма пожертвования</div>
          <input formControlName="sum" type="number">
        </div>
        <div class="form-control">
          <label>
            <input type="checkbox" formControlName="autoRenew" />
            Подписаться на ежемесячное пожертвование
          </label>
        </div>
        <div class="form-control">
          <button (click)="createPayment()">Пожертвовать</button>
        </div>
      </form>
    </div>
  </div>
</div>
