<div class="middle_stripe">
  <breadcrumb></breadcrumb>
  <div class="tab-container relative">
      <div class="dec_head _background">
        <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
        <h1 class="dec_head-title_">Пожертвование</h1>
        <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      </div>

      <div *ngIf="!success" class="profile-tabs">
        <ng-container *ngFor="let tab of tabs">
          <div class="profile-tab" [ngClass]="{'is-active': selectedTab === tab }" (click)="selectedTab = tab">
            {{tab}}
          </div>
        </ng-container>
      </div>
    </div>

  <div *ngIf="success" class="success-message">
    <div class="success-icon"></div>
    <div class="success-title">Спасибо!</div>
    <div class="success-text">Ваше пожертвование успешно обработано. Благодарим за поддержку!</div>
  </div>

  <div *ngIf="!success">
    <div *ngIf="selectedTab === 'Банковский перевод'" class="bank-transfer-section">
      <div class="bank-details">
        <div class="detail-row">
          <span class="label">Получатель:</span>
          <span class="value">Фонд "Адвайта"</span>
        </div>
        <div class="detail-row">
          <span class="label">ИНН:</span>
          <span class="value">**********</span>
        </div>
        <div class="detail-row">
          <span class="label">КПП:</span>
          <span class="value">*********</span>
        </div>
        <div class="detail-row">
          <span class="label">Расчетный счет:</span>
          <span class="value">40703810**********12</span>
        </div>
        <div class="detail-row">
          <span class="label">Банк:</span>
          <span class="value">ПАО "Сбербанк"</span>
        </div>
        <div class="detail-row">
          <span class="label">БИК:</span>
          <span class="value">*********</span>
        </div>
        <div class="detail-row">
          <span class="label">Корр. счет:</span>
          <span class="value">30101810400000000225</span>
        </div>
      </div>
      <div class="bank-note">
        <strong>Важно:</strong> При переводе обязательно укажите в назначении платежа "Пожертвование" и ваши контактные данные для получения справки о пожертвовании.
      </div>
    </div>

    <div *ngIf="selectedTab === 'Онлайн'">
      <form class="payment-form" [formGroup]="paymentForm" (ngSubmit)="createPayment()">
        <div class="form-control">
          <div>Платежная система</div>
          <div class="custom-radio-group">
            <div class="custom-radio">
              <input formControlName="type" type="radio" value="stripe" id="stripe" class="custom-radio-input">
              <label for="stripe" class="custom-radio-label">
                <span class="custom-radio-button"></span>
                <span class="radio-value">Stripe (Европа) - €</span>
              </label>
            </div>
            <div class="custom-radio">
              <input formControlName="type" type="radio" value="yookassa" id="yookassa" class="custom-radio-input">
              <label for="yookassa" class="custom-radio-label">
                <span class="custom-radio-button"></span>
                <span class="radio-value">ЮКасса (СНГ) - ₽</span>
              </label>
            </div>
          </div>
        </div>

        <div class="form-control">
          <div>Сумма пожертвования
            <span class="currency-symbol" *ngIf="paymentForm.get('type')?.value">
              {{paymentForm.get('type')?.value === 'stripe' ? '(€)' : '(₽)'}}
            </span>
          </div>
          <input
            formControlName="sum"
            type="number"
            [min]="paymentForm.get('type')?.value === 'stripe' ? 10 : 100"
            [placeholder]="paymentForm.get('type')?.value ?
              (paymentForm.get('type')?.value === 'stripe' ? 'Минимум 10 €' : 'Минимум 100 ₽') :
              'Сначала выберите платежную систему'">
        </div>

        <div class="form-control">
          <div class="checkbox-option">
            <input type="checkbox" formControlName="autoRenew" id="autoRenew">
            <label for="autoRenew">Подписаться на ежемесячное пожертвование</label>
          </div>
        </div>

        <div class="form-control">
          <button type="submit" [disabled]="!paymentForm.valid">Пожертвовать</button>
        </div>
      </form>
    </div>
  </div>
</div>
