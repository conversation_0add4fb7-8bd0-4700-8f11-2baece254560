<div class="middle_stripe">
  <breadcrumb></breadcrumb>
  <div class="tab-container relative">
      <div class="dec_head _background">
        <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
        <h1 class="dec_head-title_">Пожертвование</h1>
        <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      </div>

      <div *ngIf="!success" class="profile-tabs">
        <ng-container *ngFor="let tab of tabs">
          <div class="profile-tab" [ngClass]="{'is-active': selectedTab === tab }" (click)="selectedTab = tab">
            {{tab}}
          </div>
        </ng-container>
      </div>
    </div>

  <div *ngIf="success" class="success-message">
    <div class="success-icon"></div>
    <div class="success-title">Спасибо!</div>
    <div class="success-text">Ваше пожертвование успешно обработано. Благодарим за поддержку!</div>
  </div>

  <div *ngIf="!success">
    <div *ngIf="selectedTab === 'Банковский перевод'" class="bank-transfer-section">
      <div class="bank-details">
        <div class="detail-row">
          <span class="label">Получатель:</span>
          <span class="value">Фонд "Адвайта"</span>
        </div>
        <div class="detail-row">
          <span class="label">ИНН:</span>
          <span class="value">**********</span>
        </div>
        <div class="detail-row">
          <span class="label">КПП:</span>
          <span class="value">*********</span>
        </div>
        <div class="detail-row">
          <span class="label">Расчетный счет:</span>
          <span class="value">40703810**********12</span>
        </div>
        <div class="detail-row">
          <span class="label">Банк:</span>
          <span class="value">ПАО "Сбербанк"</span>
        </div>
        <div class="detail-row">
          <span class="label">БИК:</span>
          <span class="value">*********</span>
        </div>
        <div class="detail-row">
          <span class="label">Корр. счет:</span>
          <span class="value">30101810400000000225</span>
        </div>
      </div>
      <div class="bank-note">
        <strong>Важно:</strong> При переводе обязательно укажите в назначении платежа "Пожертвование" и ваши контактные данные для получения справки о пожертвовании.
      </div>
    </div>

    <div *ngIf="selectedTab === 'Онлайн'">
      <form class="payment-form" [formGroup]="paymentForm" (ngSubmit)="createPayment()">
        <div class="form-control">
          <div>Платежная система</div>
          <div class="radio-group">
            <div class="radio-option">
              <input formControlName="type" type="radio" value="stripe" id="stripe">
              <label for="stripe">Stripe (Европа)</label>
            </div>
            <div class="radio-option">
              <input formControlName="type" type="radio" value="yookassa" id="yookassa">
              <label for="yookassa">ЮКасса (СНГ)</label>
            </div>
          </div>
          <div *ngIf="paymentForm.get('type')?.invalid && paymentForm.get('type')?.touched" class="error-message">
            <span>Пожалуйста, выберите платежную систему</span>
          </div>
        </div>

        <div class="form-control">
          <div>Сумма пожертвования (₽)</div>
          <input formControlName="sum" type="number" min="100" placeholder="Введите сумму">
          <div *ngIf="paymentForm.get('sum')?.invalid && paymentForm.get('sum')?.touched" class="error-message">
            <span *ngIf="paymentForm.get('sum')?.errors?.['required']">Сумма обязательна для заполнения</span>
            <span *ngIf="paymentForm.get('sum')?.errors?.['min']">Минимальная сумма пожертвования: 100 ₽</span>
          </div>
        </div>

        <div class="form-control">
          <div class="checkbox-option">
            <input type="checkbox" formControlName="autoRenew" id="autoRenew">
            <label for="autoRenew">Подписаться на ежемесячное пожертвование</label>
          </div>
        </div>

        <div class="form-control">
          <button type="submit" [disabled]="!paymentForm.valid">Пожертвовать</button>
        </div>
      </form>
    </div>
  </div>
</div>
