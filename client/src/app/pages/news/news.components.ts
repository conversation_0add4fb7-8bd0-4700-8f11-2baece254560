import { BreadcrumbComponent } from "@/components/breadcrumb/breadcrumb.component";
import { CommonModule, isPlatformBrowser, NgOptimizedImage } from "@angular/common";
import { Component, inject, OnInit, PLATFORM_ID } from "@angular/core";
import {environment} from "@/env/environment";
import { AdvertisingService } from "@/services/advertising.service";

@Component({
    selector: 'app-news',
    standalone: true,
    imports: [
      CommonModule,
      BreadcrumbComponent,
      NgOptimizedImage
    ],
    templateUrl: './news.components.html',
    styleUrl: './news.components.scss'
  })
  export class NewsComponent implements OnInit {
    protected readonly environment = environment;
    advertisingService = inject(AdvertisingService);
    platformId = inject(PLATFORM_ID);
   
  
    ngOnInit() {
        if (isPlatformBrowser(this.platformId)) {
            this.advertisingService.getAll().subscribe(
                {
                    next: (res: any) => {
                    console.log('calendar', res); 
                    this.advertisings = res.filter((ad: any) => ad.active === true);
                    },
                    error: (err) => {
                        console.error('Error loading advertisements:', err);
                    }
                }
            );
        }
    }

    
  advertisings: any[] = [];

  navigateToLink(link: string) {
    if (isPlatformBrowser(this.platformId)) {
      window.open(link, '_blank');
    }
  }

  onImageError(event: any) {
    // Hide the image container if image fails to load
    const imgElement = event.target;
    const container = imgElement.closest('.ad-image-container');
    if (container) {
      container.style.display = 'none';
    }
  }
}