import { environment } from "@/env/environment";
import { PlaylistService } from "@/services/playlist.service";
import { ProfileService } from "@/services/profile.service";
import { ToasterService } from "@/services/toaster.service";
import { Component, ElementRef, EventEmitter, inject, Input, Output, ViewChild, ViewEncapsulation } from "@angular/core";
import { FormBuilder, ReactiveFormsModule } from "@angular/forms";
import { DomSanitizer } from "@angular/platform-browser";
import { Router } from "@angular/router";

@Component({
    selector: 'playlist-dialog',
    standalone: true,
    imports: [ReactiveFormsModule],
    template: `
    <dialog #playlistListDialog class="style_dialog">
        <div (click)="closeModal(playlistListDialog)" class="x_bt"></div>
        <div class="pr_20 text-center">Выберите плейлист</div>
        <ul class="h_fixed">
            @for(playlist of playlists; track playlist.id) {
            <li class="stzd">
            <label class="pr_20 flex break-all">
                <input class="item_ mt0" type="checkbox" [class.checked]="isPlaylistSelected(playlist)"
                (change)="togglePlaylistSelection(playlist)" />
                {{playlist.name}}
            </label>
            </li>
            }
        </ul>
        <div class="flex flex-col">
            <button class="button_brd mb-[20px]" (click)="playlistAddDialog.showModal()">+ Создать новый плейлист</button>
            <button class="button_brd" (click)="playlistListDialog.close(); addToPlaylistProfile()">Выбрать</button>
        </div>
    </dialog>



    <dialog class="style_dialog hght" #playlistAddDialog>
    <div (click)="closeModal(playlistAddDialog)" class="x_bt"></div>
        <div class="pr_20 mb-[25px] text-center">Создать плейлист</div>
        <form [formGroup]="formPlaylist">
            <div class="flex flex-col">
                <label class="gold_">Название</label>
                <input class="st_inp" type="text" formControlName="name">
            </div>
            <div class="flex justify-center">
                <button type="submit" class="send_button" (click)="createPlaylist()">
                    <img class="btn-backdrop-img" src="assets/images/Button1_1_ .svg" width="234" height="50" alt="bg">
                    <div class="save-btn-label">Сохранить</div>
                </button>
            </div>
        </form>
    </dialog>
    `,
    styleUrl: './playlist-dialog.component.scss',
    // encapsulation: ViewEncapsulation.ShadowDom
})
export class PlaylistDialogComponent {
    sanitizer = inject(DomSanitizer);
    router = inject(Router)
    fb = inject(FormBuilder)
    playlistService = inject(PlaylistService);
    toasterService = inject(ToasterService);
    profileService = inject(ProfileService);

    @Input() trackType: 'audio' | 'audioFile' = 'audio';

    @Input() selectedTrackId: number = -1;
    playlists: any[] = [];

    @Input()
    set showPlaylist(value: boolean) {
        if (value && this.playlistListDialog) {
            this.selectedPlaylists = [];
            this.playlistsToRemoveFrom = [];
            this.getPleylists();


            this.playlistListDialog.nativeElement.showModal();
        }
    }

    @Output() playlistSelected = new EventEmitter<any>();
    @Output() playlistClosed = new EventEmitter<any>();

    @ViewChild('playlistListDialog') playlistListDialog!: ElementRef<HTMLDialogElement>;
    @ViewChild('playlistAddDialog') playlistAddDialog!: ElementRef<HTMLDialogElement>;

    selectedPlaylists: any[] = []
    playlistsToRemoveFrom: any[] = []

    formPlaylist = this.fb.group({
        name: [null]
    })

    getPleylists() {
        this.profileService.getPlaylist().subscribe((res: any) => {
            this.playlists = res;
            this.playlists.forEach(playlist => {
                if (this.isTrackInPlaylist(playlist)) {
                    this.selectedPlaylists.push(playlist);
                }
            });
            this.playlistSelected.emit(this.playlists);

        });
    }

    createPlaylist() {
        this.playlistService.create(this.formPlaylist).subscribe((res: any) => {
            this.getPleylists();
            this.playlistsToRemoveFrom = [];
            this.selectedPlaylists = [];
            this.formPlaylist.reset();
            this.playlistAddDialog.nativeElement.close();
        })
    }


    addToPlaylistProfile() {
        for (let playlist of this.selectedPlaylists) {
            this.playlistService.add(playlist, this.selectedTrackId, this.trackType).subscribe(() => {
                this.getPleylists();
                const message = this.trackType === 'audio' ? 'Лекция добавлена в профиль!' : 'Аудио добавлено в профиль!'
                this.toasterService.showToast(message, 'success', 'bottom-middle', 3000);
            })
        }

        for (let playlist of this.playlistsToRemoveFrom) {
            this.playlistService.removeTrack(playlist.id, this.selectedTrackId).subscribe(() => {
                this.getPleylists();
            })
        }
        this.playlistsToRemoveFrom = [];
        this.selectedPlaylists = [];
        this.playlistListDialog.nativeElement.close();
    }

    closeModal(modal: HTMLDialogElement) {
        this.playlistClosed.emit(false);
        modal.close();
    }

    togglePlaylistSelection(playlist: any) {
        const index = this.selectedPlaylists.findIndex(p => p.id === playlist.id);

        if (index > -1) {
            // If it was selected and now being unselected, and it originally contained the track
            // add it to playlistsToRemoveFrom
            this.selectedPlaylists.splice(index, 1);
            if (this.isTrackInPlaylist(playlist)) {
                this.playlistsToRemoveFrom.push(playlist);
            }
        } else {
            // If it's being selected now, remove from playlistsToRemoveFrom if it was there
            this.selectedPlaylists.push(playlist);
            const removeIndex = this.playlistsToRemoveFrom.findIndex(p => p.id === playlist.id);
            if (removeIndex > -1) {
                this.playlistsToRemoveFrom.splice(removeIndex, 1);
            }
        }
    }

    isPlaylistSelected(playlist: any): boolean {
        return this.selectedPlaylists.some(p => p.id === playlist.id);
    }

    isTrackInPlaylist(playlist: any): boolean {
        if (!playlist.items || !Array.isArray(playlist.items)) {
            return false;
        }
        return playlist.items.some((item: any) => item.id === this.selectedTrackId);
    }
}
