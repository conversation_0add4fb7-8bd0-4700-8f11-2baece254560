<div>
  <div class="middle_stripe">
    <breadcrumb></breadcrumb>
    <div class="wrapper_line">
      <div class="dec_head _background">
        <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
        <h1 class="dec_head-title_">Категории</h1>
        <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      </div>
      <div class="cat_wrap">
        <ng-container>
          <ng-container>
            <div class="cat-f_wrap">
              @for(category of categories; track category.id) {
              <div class="cat-f-item_wrap">
                <div *ngIf="category.icon" class="cat-p-item">
                  <img [src]="environment.serverUrl + '/upload/' + category.icon.name">
                  <p [routerLink]="'/ru/forum/' + category.id" class="cat-badge none-dt">{{category.name}}</p>
                </div>
                <div class="cat-f-item">
                  <div class="cat-item-header">
                    <p [routerLink]="'/ru/forum/' + category.id" class="cat-badge none-md">{{category.name}}</p>
                    <div class="cat_descrpn">
                      {{category.description}}
                    </div>
                    <div class="ct_line"></div>
                    <!--            <h6 class="tt-title"><a href="page-categories-single.html">Threads - 1,245</a></h6>-->
                  </div>
                  <div class="tw_betw">
                    <span class="sd_cont">{{category.topics.length}} тем</span>
                    <span class="sd_cont">{{numCategoryComments(category.topics)}} сообщений</span>
                  </div>
                </div>
                <button [routerLink]="'/ru/forum/' + category.id" type="submit" class="confirm-btn ok-button">
                  <img class="btn-backdrop-img" ngSrc="assets/images/top_drawing1.svg" width="234" height="50" alt="bg">
                  <div class="confirm-btn-label">Подробнее</div>
                </button>
              </div>
              }
            </div>
            <div class="flex justify-center">
              <button type="submit" class="save-btn mg-auto" (click)="nextPage()" [disabled]="false">
                <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_1.svg" width="234" height="50" alt="bg">
                <div class="save-btn-label">
                  <span *ngIf="true">Загрузить еще</span>
                  <span *ngIf="false">Загрузка...</span>
                </div>
              </button>
            </div>
          </ng-container>
        </ng-container>
      </div>
    </div>
  </div>
</div>
