name: Deploy
on:
  push:
    branches:
      - dev
jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.5.3
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Deploy to server
        run: |
          ssh -o StrictHostKeyChecking=no alexander@************** -p 822 << 'EOF'
          set -e 
    
          cd /home/<USER>/advayta.org
    
          git pull origin dev
    
          podman compose down
          
          podman image prune -f
  
          podman compose build --no-cache
    
          podman compose up -d
          
          cd ~/advayta.org/client && docker cp advayta_client:/client/dist/ . 
          cd ~/advayta.org/admin && docker cp advayta_admin:/admin/dist/ . 
          
          EOF
