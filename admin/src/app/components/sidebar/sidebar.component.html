<div class="sidebar fixed bottom-0 top-0 z-50 h-full min-h-screen w-[260px] shadow-[5px_0_25px_0_rgba(94,92,154,0.1)] transition-all duration-300">
  <div class="h-full">
    <div class="flex items-center justify-center px-4 py-3">
      <a routerLink="/" class="main-logo flex shrink-0 items-center">
        <span class="align-middle text-2xl font-semibold dark:text-white-light lg:inline ltr:ml-1.5 rtl:mr-1.5">ADVAYTA</span>
      </a>
    </div>
    <ng-scrollbar class="relative !h-[calc(100vh-80px)]" appearance="compact">
      <ul class="relative space-y-1 p-4 py-0">
        <li class="nav-item">
          <a [class.active-link]="isActiveRoute('content')" routerLink="content" class="group" *ngIf="userService.hasGroup('PAGE_MODERATOR')">
            <span class="text-black dark:text-[#506690] dark:group-hover:text-white-dark">
              Страницы
            </span>
          </a>
          <a [class.active-link]="isActiveRoute('translations')" routerLink="translations" class="group" *ngIf="userService.hasGroup('TRANSLATION_MODERATOR')">
            <span class="text-black dark:text-[#506690] dark:group-hover:text-white-dark">
              Переводы
            </span>
          </a>
          <a [class.active-link]="isActiveRoute('library')" routerLink="library" class="group" *ngIf="userService.hasGroup('LIBRARY_MODERATOR')">
            <span class="text-black dark:text-[#506690] dark:group-hover:text-white-dark">
              Книги
            </span>
          </a>
          <a [class.active-link]="isActiveRoute('photo')" routerLink="photo" class="group" *ngIf="userService.hasGroup('PHOTO_MODERATOR')">
            <span class="text-black dark:text-[#506690] dark:group-hover:text-white-dark">
              Фотогалерея
            </span>
          </a>
          <a [class.active-link]="isActiveRoute('audio')" routerLink="audio" class="group" *ngIf="userService.hasGroup('LECTURE_MODERATOR')">
            <span class="text-black dark:text-[#506690] dark:group-hover:text-white-dark">
              Аудиолекции
            </span>
          </a>
          <a [class.active-link]="isActiveRoute('constructor')" routerLink="constructor" class="group" *ngIf="userService.hasGroup('CONSTRUCTOR_MODERATOR')">
            <span class="text-black dark:text-[#506690] dark:group-hover:text-white-dark">
              Конструктор лендинга
            </span>
          </a>
          <a [class.active-link]="isActiveRoute('users')" routerLink="users" class="group" *ngIf="userService.hasGroup('USER_MODERATOR')">
            <span class="text-black dark:text-[#506690] dark:group-hover:text-white-dark">
              Пользователи
            </span>
          </a>
          <a [class.active-link]="isActiveRoute('mypage')" routerLink="mypage" class="group" *ngIf="userService.hasGroup('PERSONALPAGE_MODERATOR')">
            <span class="text-black dark:text-[#506690] dark:group-hover:text-white-dark">
              Личные страницы
            </span>
          </a>
        </li>
        <li class="accordion menu nav-item">
          <button
            type="button"
            class="nav-link group w-full"
            [ngClass]="{ active: activeDropdown.includes('forum'), 'active-link': isForumActive() }"
            (click)="toggleAccordion('forum')"
            *ngIf="userService.hasGroup('FORUM_MODERATOR')"
          >
            <span class="text-black dark:text-[#506690] dark:group-hover:text-white-dark">
              Форум
            </span>
            <div [ngClass]="{ 'rtl:rotate-90 -rotate-90': !activeDropdown.includes('forum') }">
              <icon-caret-down />
            </div>
          </button>
          <div [ngClass]="{'hidden': !activeDropdown.includes('forum')}" class="accordion-content">
            <ul class="sub-menu text-gray-500">
              <li>
                <a routerLink="/forum/categories" routerLinkActive="active"
                >Категории</a
                >
              </li>
            </ul>
          </div>
        </li>
        <li class="nav-item">
          <a [class.active-link]="isActiveRoute('advertising')" routerLink="advertising" class="group" *ngIf="userService.hasGroup('CALENDAR_MODERATOR')">
            <span class="text-black dark:text-[#506690] dark:group-hover:text-white-dark">
              Календарь
            </span>
          </a>
        </li>
        <li class="nav-item">
          <a [class.active-link]="isActiveRoute('audiofiles')" routerLink="audiofiles" class="group" *ngIf="userService.hasGroup('AUDIO_MODERATOR')">
            <span class="text-black dark:text-[#506690] dark:group-hover:text-white-dark">
              Аудио
            </span>
          </a>
        </li>
      </ul>
    </ng-scrollbar>
  </div>
</div>
