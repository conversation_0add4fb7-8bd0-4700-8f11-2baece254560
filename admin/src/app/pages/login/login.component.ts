import { Component, inject } from '@angular/core';
import {Router} from '@angular/router';
import {FormControl, FormGroup, Validators} from "@angular/forms";
import {ReactiveFormsModule} from '@angular/forms';
import {AuthService} from "@/services/auth.service";

@Component({
    selector: 'app-root',
    imports: [ReactiveFormsModule],
    templateUrl: './login.component.html',
    styleUrl: './login.component.scss'
})
export class LoginComponent {
    router = inject(Router)
    authService = inject(AuthService);
    form = new FormGroup({
        email: new FormControl('', Validators.required),
        password: new FormControl('', Validators.required)
    })

    onSubmit() {
        this.authService.login(this.form.value).subscribe(() => this.router.navigate(['/']))
        return false;
    }
}
