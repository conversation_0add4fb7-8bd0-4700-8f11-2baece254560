<div class="admin-component">
  <!-- Page Header -->
  <div class="admin-page-header">
    <div>
      <h1 class="admin-page-title">Категории форума</h1>
    </div>
    <div class="admin-actions">
      <button class="btn btn-primary" routerLink="/forum/categories/add">Создать категорию</button>
    </div>
  </div>

  <!-- Modals -->
  <dialog #modal class="admin-modal">
    <div class="admin-modal-content">
      <div>{{message}}</div>
      <div class="admin-modal-footer">
        <button (click)="closeModal(modal)" class="btn btn-primary">Да</button>
      </div>
    </div>
  </dialog>

  <dialog #confirmDialog class="admin-modal">
    <div class="admin-modal-content">
      <div>{{ message }}</div>
      <div class="admin-modal-footer">
        <button class="btn btn-danger">Да</button>
        <button class="btn btn-outline-secondary">Отмена</button>
      </div>
    </div>
  </dialog>

  <!-- Content -->
  <div class="admin-content-wrapper">
    <div class="table-responsive">
      <table>
        <thead>
          <tr>
            <th>Название</th>
            <th>Описание</th>
            <th>Сортировка</th>
            <th class="text-center w-64">Действия</th>
          </tr>
        </thead>
        <tbody>
          @for(category of categories; track category.id) {
            <tr>
              <td>{{category.name}}</td>
              <td>{{category.description}}</td>
              <td>{{category.sort}}</td>
              <td>
                <div class="admin-table-actions">
                  <button class="btn btn-sm btn-primary" (click)="router.navigate(['/forum/categories/' + category.id])">Редактировать</button>
                  <button class="btn btn-sm btn-danger" (click)="deleteCategory(category.id)">Удалить</button>
                </div>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  </div>
</div>
