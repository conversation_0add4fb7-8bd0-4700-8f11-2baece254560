<div class="panel">
  <!-- Admin Dialog Component -->
  <admin-dialog></admin-dialog>

  <div class="mb-5">
    <div class="mt-3 flex flex-wrap border-b border-white-light dark:border-[#191e3a]">
      @for(tab of tabs; track $index) {
        <a
          href="javascript:"
          class="-mb-[1px] block border border-transparent p-3.5 py-2 !outline-none transition duration-300 hover:text-primary dark:hover:border-b-black"
          [ngClass]="{ '!border-white-light !border-b-white  text-primary dark:!border-[#191e3a] dark:!border-b-black': selectedTab === $index }"
          (click)="selectedTab = $index"
        >
          {{tab}}
        </a>
      }
    </div>
  </div>

  <form class="profile-form space-y-3" [formGroup]="user">
    <div class="form-group tab-content" [ngClass]="{'tab-active': selectedTab == 0}">
      <div class="form-group__fields space-y-3">
        <div>
          Последняя активность: {{lastActivity}}
        </div>
        <div class="flex">
          <input type="checkbox" class="form-checkbox" formControlName="active">
          <label>Активность</label>
        </div>
        <div class="flex">
          <input type="checkbox" class="form-checkbox" formControlName="confirmed">
          <label>Подтвержден</label>
        </div>
        <div class="form-group__field">
          <label>Имя</label>
          <input class="form-input" formControlName="firstName" type="text">
        </div>
        <div class="form-group__field">
          <label>Фамилия</label>
          <input class="form-input" formControlName="lastName" type="text">
        </div>
        <div class="form-group__field">
          <label>Отчество</label>
          <input class="form-input" formControlName="middleName" type="text">
        </div>
        <div class="form-group__field">
          <label>E-mail *</label>
          <input class="form-input" formControlName="email" type="text" readonly>
        </div>
        <div class="form-group__field">
          <label>Духовное имя</label>
          <input class="form-input" formControlName="spiritualName" type="text">
        </div>
        <div class="form-group__field">
          <label>Духовный статус</label>
          <p-multiselect
            *ngIf="statuses"
            [options]="statuses"
            formControlName="statuses"
            optionValue="value"
            optionLabel="label"
            [filter]="true"
            placeholder="Выберите духовный статус"
            [showClear]="true"
            display="chip"
            [maxSelectedLabels]="10"
            (onChange)="onGroupChange($event)"
            class="w-full">
          </p-multiselect>
        </div>
        <div class="form-group__field">
          <label>Группа</label>
          <p-multiselect
          *ngIf="groups"
          [options]="groups"
          formControlName="groups"
          optionValue="value"
          optionLabel="label"
          [filter]="true"
          placeholder="Выберите группу"
          [showClear]="true"
          display="chip"
          [maxSelectedLabels]="10"
          class="w-full">
        </p-multiselect>
        </div>
        <div class="form-group__field">
          <label>Дата рождения</label>
          <input class="form-input" formControlName="birthDate" type="date">
        </div>
        <div class="form-group__field">
          <label>Страна</label>
          <p-select [options]="countries" formControlName="country" optionValue="iso_code2" optionLabel="name_ru" [filter]="true" filterBy="name_ru" [showClear]="true" placeholder="Select a Country" class="w-full md:w-56">
            <ng-template #selectedItem let-selectedOption>
                <div class="flex items-center gap-2">
                    <img [src]="selectedOption.flag_url" style="width: 18px" />
                    <div>{{ selectedOption.name_ru }}</div>
                </div>
            </ng-template>
            <ng-template let-country #item>
                <div class="flex items-center gap-2">
                    <img [src]="country.flag_url"  style="width: 18px" />
                    <div>{{ country.name_ru }}</div>
                </div>
            </ng-template>
        </p-select>
        </div>
        <div class="form-group__field">
          <label>Город</label>
          <input class="form-input" formControlName="city" type="text">
        </div>
        <div class="form-group__field">
          <label>Адрес</label>
          <input class="form-input" formControlName="address" type="text">
        </div>
        <div class="form-group__field">
          <label>Телефон</label>
          <input class="form-input" formControlName="phone" type="text">
        </div>
        <div class="form-group__field">
          <label>Телеграм</label>
          <input class="form-input" formControlName="telegram" type="text">
        </div>
        <div class="form-group__field">
          <label>Язык общения</label>
          <input class="form-input" formControlName="language" type="text">
        </div>
        <div *ngIf="user.value.avatar">
          <label>Фотография</label>
          <img width="100" height="100" class="profile-avatar" *ngIf="user.value.avatar" [src]="environment.serverUrl + '/upload/' + user.value.avatar!.name" alt="">
        </div>
        <div>
          <label>Комментарий</label>
          <textarea class="form-textarea" formControlName="comment"></textarea>
        </div>
      </div>
    </div>
    <div class="form-group tab-content" [ngClass]="{'tab-active': selectedTab == 1}">
      <div class="form-group__fields space-y-3">
        <div class="form-group__field">
          <label>Дата принятия Символа веры / Прибежища</label>
          <input class="form-input" formControlName="dpDate" type="date">
        </div>
        <div class="form-group__field">
          <label>Практиковали ли в другой традиции, школе, под чьим-то руководством?</label>
          <input class="form-input" formControlName="dpPractice" type="text">
        </div>
        <div class="form-group__field">
          <label>Как оцениваете свой уровень практики, были ли духовные, мистические опыты?</label>
          <input class="form-input" formControlName="dpLevel" type="text">
        </div>
        <div class="form-group__field">
          <label>Принимали ли ранее участие в мероприятиях нашей общины?</label>
          <input class="form-input" formControlName="dpEvents" type="text">
        </div>
        <div class="form-group__field">
          <label>Как вы познакомились с нашей традицией и общиной?</label>
          <input class="form-input" formControlName="dpMeet" type="text">
        </div>
        <div class="form-group__field">
          <label>Цели практики</label>
          <select class="form-select" formControlName="dpGoal">
            <option selected value="Гость (познакомиться с традицией)">Гость (познакомиться с традицией)</option>
            <option value="Принятие через вашу школу индуизма как своей религии">Принятие через вашу школу индуизма как своей религии</option>
            <option value="Грихастха (стать учеником Гуру с минимальным количеством обетов)">Грихастха (стать учеником Гуру с минимальным количеством обетов)</option>
            <option value="Карма-санньяса (стать учеником Гуру с более строгими обетами)">Карма-санньяса (стать учеником Гуру с более строгими обетами)</option>
            <option value="Пурна-санньяса (стать монахом)">Пурна-санньяса (стать монахом)</option>
            <option value="Ванапрастха (статус отшельников, принимаемый после 50 лет с супругой или одиночно)">Ванапрастха (статус отшельников, принимаемый после 50 лет с супругой или одиночно)</option>
          </select>
        </div>
      </div>
    </div>
    <div class="form-group tab-content" [ngClass]="{'tab-active': selectedTab == 2}">
      <div class="form-group__fields space-y-3">
        <div class="form-group__field">
          <label>Образование</label>
          <select class="form-select" formControlName="education">
            <option value="не выбрано">не выбрано</option>
            <option value="среднее">среднее</option>
            <option value="среднее-специальное">среднее-специальное</option>
            <option value="неоконченное высшее">неоконченное высшее</option>
            <option value="высшее">высшее</option>
          </select>
        </div>
        <div class="form-group__field">
          <label>Специальность</label>
          <input class="form-input" formControlName="speciality" type="text">
        </div>
        <div class="form-group__field">
          <label>Профессия</label>
          <input class="form-input" formControlName="profession" type="text">
        </div>
        <div class="form-group__field">
          <label>Навыки</label>
          <input class="form-input" formControlName="skills" type="text">
        </div>
        <div class="form-group__field">
          <label>Служение</label>
          <input class="form-input" formControlName="service" type="text">
        </div>
      </div>
    </div>
    <div class="form-group tab-content" [ngClass]="{'tab-active': selectedTab == 3}">
      <div class="form-group__fields space-y-3">
        <div class="form-group__field">
          <label>Состояние здоровья</label>
          <select class="form-select" formControlName="health">
            <option selected value="Совершенно здоров">Совершенно здоров</option>
            <option value="Относительно здоров">Относительно здоров</option>
            <option value="Есть ограничения на физические нагрузки">Есть ограничения на физические нагрузки</option>
            <option value="Есть хронические заболевания">Есть хронические заболевания</option>
            <option value="Есть психиатрические заболевания">Есть психиатрические заболевания</option>
            <option value="Есть зависимость (алкогольная, наркотическая)">Есть зависимость (алкогольная, наркотическая)</option>
          </select>
        </div>
      </div>
    </div>
    <div class="form-group tab-content" [ngClass]="{'tab-active': selectedTab == 4}">
      <div class="form-group__fields space-y-3">
        <div class="form-group__field">
          <label>Подобрать инструктора в ближайшем городе? </label>
          <div>
            <input class="form-radio" type="radio" formControlName="supportInstructor" value="Да"> Да
          </div>
          <div>
            <input class="form-radio" type="radio" formControlName="supportInstructor" value="Нет"> Нет
          </div>
        </div>
        <div class="form-group__field">
          <label>Есть ли необходимость в консультации монаха?</label>
          <div>
            <input class="form-radio" type="radio" formControlName="supportConsultation" value="Да"> Да
          </div>
          <div>
            <input class="form-radio" type="radio" formControlName="supportConsultation" value="Нет"> Нет
          </div>
        </div>
        <div class="form-group__field">
          <label>Хотели бы вы поступить на заочное обучение?</label>
          <select class="form-select" formControlName="supportCorrespondence">
            <option value="Да">Да</option>
            <option value="Нет">Нет</option>
          </select>
        </div>
      </div>
    </div>
    <div class="flex gap-2 mt-10">
      <button [disabled]="user.invalid" class="btn btn-primary" (click)="saveUser()">Сохранить</button>
      <button class="btn btn-danger" (click)="deleteUser()">Удалить</button>
      <button class="btn" (click)="router.navigate(['/users'])">Закрыть</button>
    </div>
  </form>
</div>
